<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Scanner - ABC Travels Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://unpkg.com/html5-qrcode"></script>
    <link rel="icon" type="image/svg+xml" href="../logo.svg">
    <script src="../config.js"></script>
    <style>
        #reader {
            width: 100% !important;
        }
        #reader video {
            max-width: 100% !important;
        }
        .result {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-top: 20px;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Check Authentication -->
    <script>
        if (!localStorage.getItem('adminAuthenticated')) {
            window.location.href = 'login.html';
        }
    </script>

    <div class="min-h-screen">
        <!-- Header -->
        <header class="bg-white shadow">
            <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center">
                    <h1 class="text-3xl font-bold text-gray-900">
                        Ticket Scanner
                    </h1>
                    <a href="dashboard.html" class="text-primary hover:text-secondary">
                        Back to Dashboard
                    </a>
                </div>
            </div>
        </header>

        <!-- Main Content -->
            <main class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Scanner Section -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-xl font-semibold text-gray-900">Scan Ticket</h2>
                        <div class="space-x-2">
                            <button id="startButton" onclick="startScanner()" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2">
                                Start Scanner
                            </button>
                            <button id="stopButton" onclick="stopScanner()" class="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 hidden">
                                Stop Scanner
                            </button>
                        </div>
                    </div>
                    <div id="reader" class="mb-4"></div>
                    <div id="scanStatus" class="text-sm text-gray-500 text-center"></div>
                    </div>

                    <!-- Recent Scans -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Recent Scans</h2>
                    <div id="recentScans" class="space-y-4">
                        <!-- Recent scans will be added here -->
                    </div>
                    </div>
                </div>
            </main>
    </div>

    <script>
        let html5QrcodeScanner = null;
        let ws = null;
        let isScanning = false;

        // Connect to WebSocket
        function connectWebSocket() {
            ws = new WebSocket(currentConfig.wsUrl);
            
            ws.onopen = () => {
                console.log('Connected to WebSocket server');
                updateStatus('Ready to scan');
            };
            
            ws.onclose = () => {
                console.log('WebSocket connection closed');
                updateStatus('Connection lost. Reconnecting...');
                setTimeout(connectWebSocket, 5000);
            };
            
            ws.onerror = (error) => {
                console.error('WebSocket error:', error);
                updateStatus('Connection error');
            };
            
            ws.onmessage = (event) => {
                handleWebSocketMessage(event);
            };
        }

        // Handle WebSocket messages
        function handleWebSocketMessage(event) {
                try {
                    const data = JSON.parse(event.data);
                console.log('Received message:', data);
                    
                    switch (data.type) {
                        case 'VERIFICATION_RESULT':
                            handleVerificationResult(data.data);
                            break;
                        case 'ERROR':
                            showError(data.error);
                            break;
                    }
                } catch (error) {
                console.error('Error handling message:', error);
                    showError('Failed to process server response');
                }
        }

        // Handle verification result
        function handleVerificationResult(result) {
            // Close the loading dialog
            Swal.close();

            if (result.success) {
                const booking = result.booking_data.booking;
                const route = result.booking_data.route;
                
                // Add to recent scans
                addRecentScan({
                    status: 'Valid',
                    customer: booking.customer_name,
                    route: `${route.from_location} → ${route.to_location}`,
                    time: new Date().toLocaleTimeString(),
                    message: result.message
                });

                // Play success sound
                const audio = new Audio('data:audio/mp3;base64,SUQzBAAAAAAAI1RTU0UAAAAPAAADTGF2ZjU4Ljc2LjEwMAAAAAAAAAAAAAAA/+M4wAAAAAAAAAAAAEluZm8AAAAPAAAAAwAAAbAAkJCQkJCQkJCQkJCQkJCQwMDAwMDAwMDAwMDAwMDAwMD//////////////////8AAAA5TEFNRTMuMTAwAZYAAAAAAAAAABQ4JAMGQgAAQAAAAbAAAACggmf/4zjMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAWGluZwAAAA8AAAACAAADwADg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg////////////////////////////////////////////////////////////////8AAAAATGF2YzU4LjEzAAAAAAAAAAAAAAAAJAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/+MYxAAAAANIAAAAAExBTUUzLjEwMFVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV/+MYxDsAAANIAAAAAFVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV');
                audio.play();

                // Show success message
                Swal.fire({
                    icon: 'success',
                    title: 'Valid Ticket',
                    html: `
                        <div class="text-left">
                            <p><strong>Customer:</strong> ${booking.customer_name}</p>
                            <p><strong>Route:</strong> ${route.from_location} → ${route.to_location}</p>
                            <p><strong>Travel Date:</strong> ${new Date(booking.travel_date).toLocaleDateString()}</p>
                            <p><strong>Passengers:</strong> ${booking.num_passengers}</p>
                            <p><strong>Departure:</strong> ${route.departure_time}</p>
                            <p class="mt-4 text-sm text-green-600">${result.message}</p>
                        </div>
                    `,
                    confirmButtonColor: '#1a56db'
                }).then(() => {
                    resumeScanning();
                });
            } else {
                // Play error sound
                const audio = new Audio('data:audio/mp3;base64,SUQzBAAAAAAAI1RTU0UAAAAPAAADTGF2ZjU4Ljc2LjEwMAAAAAAAAAAAAAAA/+M4wAAAAAAAAAAAAEluZm8AAAAPAAAAAwAAAeAAUFBQUFBQUFBQUFBQUFBQgICAgICAgICAgICAgICAgKCgoKCgoKCgoKCgoKCgoKD//////////////////8AAAA5TEFNRTMuMTAwAZYAAAAAAAAAABQ4JAMGQgAAQAAAAeAAAAKQQzX/4zjMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAWGluZwAAAA8AAAACAAADwADw8PDw8PDw8PDw8PDw8PDw8PDw8PDw8PDw8PDw8PDw////////////////////////////////////////////////////////////////8AAAAATGF2YzU4LjEzAAAAAAAAAAAAAAAAJAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/+MYxAAAAANIAAAAAExBTUUzLjEwMFVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV/+MYxDsAAANIAAAAAFVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV');
                audio.play();

                // Only add to recent scans if it's a validation error (not a format error)
                if (result.booking_data) {
                    const booking = result.booking_data.booking;
                    const route = result.booking_data.route;
                    addRecentScan({
                        status: 'Invalid',
                        customer: booking.customer_name,
                        route: `${route.from_location} → ${route.to_location}`,
                        time: new Date().toLocaleTimeString(),
                        message: result.message
                    });
                }

                // Show error message
                Swal.fire({
                    icon: 'error',
                    title: 'Invalid Ticket',
                    text: result.message,
                    confirmButtonColor: '#1a56db'
                }).then(() => {
                    resumeScanning();
                });
            }
        }

        // Start scanner
        function startScanner() {
            if (html5QrcodeScanner) {
                html5QrcodeScanner.clear();
            }

            const config = {
                fps: 10,
                qrbox: { width: 250, height: 250 },
                aspectRatio: 1.0
            };

            html5QrcodeScanner = new Html5Qrcode("reader");
            html5QrcodeScanner.start(
                { facingMode: "environment" },
                config,
                onScanSuccess,
                onScanError
            ).then(() => {
                isScanning = true;
                updateButtonStates();
                updateStatus('Scanner active');
            }).catch((err) => {
                console.error('Failed to start scanner:', err);
                showError('Failed to start scanner. Please check camera permissions.');
            });
        }

        // Stop scanner
        function stopScanner() {
            if (html5QrcodeScanner && isScanning) {
                html5QrcodeScanner.stop().then(() => {
                    isScanning = false;
                    updateButtonStates();
                    updateStatus('Scanner stopped');
                }).catch((err) => {
                    console.error('Failed to stop scanner:', err);
                });
            }
        }

        // Resume scanning
        function resumeScanning() {
            if (html5QrcodeScanner && !isScanning) {
                startScanner();
            }
        }

        // Handle successful scan
        function onScanSuccess(decodedText) {
            if (!isScanning) return;
            
            try {
                let ticketData;
                try {
                    // Trim any whitespace and try to parse
                    ticketData = JSON.parse(decodedText.trim());
                    console.log('Parsed ticket data:', ticketData);
                } catch (e) {
                    console.error('Failed to parse QR code data:', e);
                    showError('Invalid QR code format. Please scan a valid ticket QR code.');
                    resumeScanning();
                    return;
                }

                // Validate ticket data structure
                if (!ticketData || typeof ticketData !== 'object') {
                    showError('Invalid QR code format. Please scan a valid ticket QR code.');
                    resumeScanning();
                    return;
                }

                // Validate booking ID
                if (!ticketData.bookingId || typeof ticketData.bookingId !== 'string') {
                    showError('Invalid ticket format: Missing or invalid booking ID.');
                    resumeScanning();
                    return;
                }

                // Validate ticket type
                if (!ticketData.type || ticketData.type !== 'ticket') {
                    showError('Invalid ticket format: Not a valid ABC Travels ticket.');
                    resumeScanning();
                    return;
                }

                console.log('Valid ticket data found:', ticketData);
                stopScanner();
                updateStatus('Verifying ticket...');

                // Show verification in progress
                Swal.fire({
                    title: 'Verifying Ticket',
                    text: 'Please wait...',
                    allowOutsideClick: false,
                    showConfirmButton: false,
                    willOpen: () => {
                        Swal.showLoading();
                    }
                });

                // Send to server for verification
                ws.send(JSON.stringify({
                    type: 'SCAN_RESULT',
                    data: {
                        bookingId: ticketData.bookingId,
                        scannedBy: localStorage.getItem('adminUser') || 'unknown',
                        location: 'Terminal',
                        deviceInfo: navigator.userAgent
                    }
                }));
            } catch (error) {
                console.error('Error processing QR code:', error);
                showError('Failed to process QR code. Please try again.');
                resumeScanning();
            }
        }

        // Handle scan errors
        function onScanError(error) {
            if (error !== 'QR code parse error') {
                console.error('Scanner error:', error);
            }
        }

        // Show error message
        function showError(message) {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: message,
                confirmButtonColor: '#1a56db'
            }).then(() => {
                resumeScanning();
            });
        }

        // Update scanner status
        function updateStatus(message) {
            document.getElementById('scanStatus').textContent = message;
        }

        // Update button states
        function updateButtonStates() {
            document.getElementById('startButton').classList.toggle('hidden', isScanning);
            document.getElementById('stopButton').classList.toggle('hidden', !isScanning);
        }

        // Add recent scan
        function addRecentScan(scan) {
            const recentScans = document.getElementById('recentScans');
            const scanElement = document.createElement('div');
            scanElement.className = 'bg-gray-50 rounded-lg p-4';
            
            if (scan.status === 'Valid') {
                scanElement.innerHTML = `
                    <div class="flex items-center justify-between">
                        <div>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Valid
                            </span>
                            <p class="mt-1 text-sm text-gray-900">${scan.customer}</p>
                            <p class="text-sm text-gray-500">${scan.route}</p>
                            <p class="text-xs text-green-600 mt-1">${scan.message}</p>
                        </div>
                        <span class="text-xs text-gray-500">${scan.time}</span>
                    </div>
                `;
            } else {
                scanElement.innerHTML = `
                    <div class="flex items-center justify-between">
                        <div>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                Invalid
                            </span>
                            <p class="mt-1 text-sm text-red-600">${scan.error}</p>
                            <p class="text-xs text-red-500 mt-1">${scan.message}</p>
                        </div>
                        <span class="text-xs text-gray-500">${scan.time}</span>
                    </div>
                `;
            }
            
            recentScans.insertBefore(scanElement, recentScans.firstChild);
            
            // Keep only last 10 scans
            while (recentScans.children.length > 10) {
                recentScans.removeChild(recentScans.lastChild);
            }
        }

        // Initialize
        window.addEventListener('load', () => {
            if (!localStorage.getItem('adminAuthenticated')) {
                window.location.href = 'login.html';
            } else {
                connectWebSocket();
                updateButtonStates();
            }
        });

        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            if (html5QrcodeScanner) {
                html5QrcodeScanner.stop().catch(console.error);
            }
            if (ws) {
                ws.close();
            }
        });
    </script>
</body>
</html> 