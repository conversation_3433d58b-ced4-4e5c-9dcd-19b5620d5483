<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Available Routes - ABC Travels</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://js.paystack.co/v1/inline.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/qrcodejs/1.0.0/qrcode.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/html2canvas@1.4.1/dist/html2canvas.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <link rel="icon" type="image/svg+xml" href="logo.svg">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1a56db',
                        secondary: '#1e429f',
                        accent: '#e3f2fd'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg fixed w-full z-50">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <a href="/" class="flex items-center">
                        <img src="logo.svg" alt="ABC Travels Logo" class="h-8 w-auto">
                    </a>
                </div>
                <div class="hidden md:flex items-center space-x-4">
                    <a href="/" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">Home</a>
                    <a href="/routes.html" class="text-primary px-3 py-2 rounded-md text-sm font-medium">Routes</a>
                    <a href="/about.html" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">About</a>
                    <a href="/faq.html" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">FAQ</a>
                    <a href="/privacy.html" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">Privacy Policy</a>
                    <a href="/contact.html" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">Contact</a>
                </div>
                <div class="md:hidden flex items-center">
                    <button id="mobile-menu-button" class="text-gray-700 hover:text-primary">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        <!-- Mobile Menu -->
        <div id="mobile-menu" class="hidden md:hidden bg-white">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="/" class="block text-gray-700 hover:text-primary px-3 py-2 rounded-md text-base font-medium">Home</a>
                <a href="/routes.html" class="block text-primary px-3 py-2 rounded-md text-base font-medium">Routes</a>
                <a href="/about.html" class="block text-gray-700 hover:text-primary px-3 py-2 rounded-md text-base font-medium">About</a>
                <a href="/faq.html" class="block text-gray-700 hover:text-primary px-3 py-2 rounded-md text-base font-medium">FAQ</a>
                <a href="/privacy.html" class="block text-gray-700 hover:text-primary px-3 py-2 rounded-md text-base font-medium">Privacy Policy</a>
                <a href="/contact.html" class="block text-gray-700 hover:text-primary px-3 py-2 rounded-md text-base font-medium">Contact</a>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <header class="bg-gradient-to-r from-primary to-secondary pt-24 pb-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h1 class="text-3xl font-bold text-white">Available Routes</h1>
            <p class="mt-2 text-gray-200">Find and book your perfect journey across Ghana</p>
        </div>
    </header>

    <!-- Search and Filter Section -->
    <section class="py-8 bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="from" class="block text-sm font-medium text-gray-700">From</label>
                    <select id="from" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary rounded-md">
                        <option value="">Select departure city</option>
                        <option value="accra">Accra</option>
                        <option value="kumasi">Kumasi</option>
                        <option value="takoradi">Takoradi</option>
                        <option value="tamale">Tamale</option>
                    </select>
                </div>
                <div>
                    <label for="to" class="block text-sm font-medium text-gray-700">To</label>
                    <select id="to" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary rounded-md">
                        <option value="">Select destination city</option>
                        <option value="accra">Accra</option>
                        <option value="kumasi">Kumasi</option>
                        <option value="takoradi">Takoradi</option>
                        <option value="tamale">Tamale</option>
                    </select>
                </div>
                <div>
                    <label for="date" class="block text-sm font-medium text-gray-700">Date</label>
                    <input type="date" id="date" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary rounded-md">
                </div>
                <div class="flex items-end">
                    <button onclick="searchRoutes()" class="w-full bg-primary text-white px-4 py-2 rounded-md hover:bg-secondary transition-colors">
                        Search Routes
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Routes List -->
    <section class="py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div id="routes-container" class="grid grid-cols-1 gap-6">
                <!-- Routes will be dynamically populated here -->
            </div>
        </div>
    </section>

    <!-- Booking Modal -->
    <div id="booking-modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 hidden">
        <div class="flex items-center justify-center min-h-screen">
            <div class="bg-white rounded-lg p-8 max-w-md w-full mx-4">
                <h2 class="text-2xl font-bold mb-4">Book Your Ticket</h2>
                <form id="booking-form" class="space-y-4" novalidate>
                    <div>
                        <label for="customer_name" class="block text-sm font-medium text-gray-700">Full Name</label>
                        <input type="text" id="customer_name" name="customer_name" required class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary">
                    </div>
                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700">Phone</label>
                        <input type="tel" id="phone" name="phone" required class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary">
                    </div>
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
                        <input type="email" id="email" name="email" required class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary">
                    </div>
                    <div>
                        <label for="travel_date" class="block text-sm font-medium text-gray-700">Travel Date</label>
                        <input type="date" id="travel_date" name="travel_date" required class="mt-1 block w-full border-gray-300 rounded-md">
                    </div>
                    <div>
                        <label for="num_passengers" class="block text-sm font-medium text-gray-700">Number of Passengers</label>
                        <div class="flex items-center space-x-2">
                            <button type="button" onclick="decrementPassengers()" class="px-3 py-1 bg-gray-100 rounded-md hover:bg-gray-200">-</button>
                            <input type="number" id="num_passengers" name="num_passengers" value="1" min="1" max="10" required class="w-20 px-3 py-2 text-center border border-gray-300 rounded-md focus:ring-primary focus:border-primary">
                            <button type="button" onclick="incrementPassengers()" class="px-3 py-1 bg-gray-100 rounded-md hover:bg-gray-200">+</button>
                            <span class="text-sm text-gray-500 ml-2">(Max: 10 seats)</span>
                        </div>
                    </div>
                    <div class="flex justify-end space-x-4">
                        <button type="button" onclick="closeBookingModal()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md">
                            Cancel
                        </button>
                        <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-primary hover:bg-secondary rounded-md">
                            Proceed to Payment
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-gray-800">
        <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Company Info -->
                <div class="col-span-1">
                    <img src="logo.svg" alt="ABC Travels Logo" class="h-8 w-auto filter invert">
                    <p class="mt-4 text-gray-300">
                        Your trusted partner for comfortable and reliable bus travel across Ghana.
                    </p>
                </div>
                <!-- Quick Links -->
                <div class="col-span-1">
                    <h3 class="text-white text-lg font-bold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="/routes.html" class="text-gray-300 hover:text-white">Routes</a></li>
                        <li><a href="/about.html" class="text-gray-300 hover:text-white">About Us</a></li>
                        <li><a href="/faq.html" class="text-gray-300 hover:text-white">FAQ</a></li>
                        <li><a href="/contact.html" class="text-gray-300 hover:text-white">Contact</a></li>
                    </ul>
                </div>
                <!-- Contact Info -->
                <div class="col-span-1">
                    <h3 class="text-white text-lg font-bold mb-4">Contact Us</h3>
                    <ul class="space-y-2">
                        <li class="text-gray-300">Phone: +233 XX XXX XXXX</li>
                        <li class="text-gray-300">Email: <EMAIL></li>
                        <li class="text-gray-300">Address: Accra, Ghana</li>
                    </ul>
                </div>
                <!-- Social Media -->
                <div class="col-span-1">
                    <h3 class="text-white text-lg font-bold mb-4">Follow Us</h3>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-300 hover:text-white">
                            <span class="sr-only">Facebook</span>
                            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                <path fill-rule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clip-rule="evenodd"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-300 hover:text-white">
                            <span class="sr-only">Twitter</span>
                            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-300 hover:text-white">
                            <span class="sr-only">Instagram</span>
                            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                <path fill-rule="evenodd" d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" clip-rule="evenodd"/>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
            <div class="mt-8 border-t border-gray-700 pt-8">
                <p class="text-center text-gray-400">
                    © 2025 ABC Travels. All rights reserved. | Developed by Procurement and Supply Group 13
                </p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="config.js"></script>
    <script>
        // Initialize Supabase client
        const { createClient } = supabase;
        const supabaseClient = createClient(
            currentConfig.supabaseUrl,
            currentConfig.supabaseKey
        );

        async function loadRoutes() {
            try {
                const { data: routes, error } = await supabaseClient
                    .from('routes')
                    .select('*')
                    .eq('is_active', true)
                    .order('from_location', { ascending: true });

                if (error) throw error;

                const routesContainer = document.getElementById('routes-container');
                routesContainer.innerHTML = '';

                routes.forEach(route => {
                    const routeCard = document.createElement('div');
                    routeCard.className = 'bg-white rounded-lg shadow-md overflow-hidden';
                    routeCard.innerHTML = `
                        <div class="p-6">
                            <div class="flex justify-between items-start">
                                <div>
                                    <h3 class="text-xl font-semibold text-gray-900">
                                        ${route.from_location} → ${route.to_location}
                                    </h3>
                                    <p class="mt-2 text-gray-600">
                                        Departure: ${route.departure_time}
                                    </p>
                                    <p class="text-gray-600">Max Allowed Luggage: ${route.distance_km} kg</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-2xl font-bold text-primary">
                                        GH₵ ${route.price.toFixed(2)}
                                    </p>
                                    <p class="text-sm text-gray-500">
                                        ${route.available_seats} seats available
                                    </p>
                                </div>
                            </div>
                            <div class="mt-4">
                                <p class="text-gray-600 text-sm">
                                    ${route.description || 'No additional information available.'}
                                </p>
                            </div>
                            <div class="mt-6">
                                <button onclick="bookRoute('${route.id}')" 
                                    class="w-full bg-primary text-white px-4 py-2 rounded-md hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 disabled:opacity-50"
                                    ${route.available_seats === 0 ? 'disabled' : ''}>
                                    ${route.available_seats === 0 ? 'Sold Out' : 'Book Now'}
                                </button>
                            </div>
                        </div>
                    `;
                    routesContainer.appendChild(routeCard);
                });
            } catch (error) {
                console.error('Error loading routes:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Failed to load routes',
                    confirmButtonColor: '#1a56db'
                });
            }
        }

        async function bookRoute(routeId) {
            try {
                const { data: route, error: routeError } = await supabaseClient
                    .from('routes')
                    .select('*')
                    .eq('id', routeId)
                    .single();

                if (routeError) throw routeError;

                const { value: formValues } = await Swal.fire({
                    title: 'Book Your Ticket',
                    html: `
                        <div class="bg-white p-6 rounded-lg">
                            <!-- Route Information -->
                            <div class="bg-gray-50 p-4 rounded-lg mb-6">
                                <div class="flex justify-between items-center mb-2">
                                    <h3 class="text-lg font-semibold text-gray-900">
                                        ${route.from_location} → ${route.to_location}
                                    </h3>
                                    <span class="text-lg font-bold text-primary">
                                        GH₵ ${route.price.toFixed(2)}
                                    </span>
                                </div>
                                <div class="text-sm text-gray-600">
                                    <p>Departure: ${route.departure_time}</p>
                                    <p>Max Allowed Luggage: ${route.distance_km} kg</p>
                                </div>
                            </div>

                            <!-- Simple Booking Form -->
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1" for="swal-customer_name">Full Name</label>
                                    <input type="text" id="swal-customer_name" 
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1" for="swal-phone">Phone Number</label>
                                    <input type="tel" id="swal-phone"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1" for="swal-email">Email Address</label>
                                    <input type="email" id="swal-email"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary">
                                </div>
                            </div>
                        </div>
                    `,
                    showCancelButton: true,
                    confirmButtonText: 'Proceed to Payment',
                    cancelButtonText: 'Cancel',
                    confirmButtonColor: '#1a56db',
                    cancelButtonColor: '#374151',
                    focusConfirm: false,
                    preConfirm: () => {
                        const customerName = document.getElementById('swal-customer_name').value.trim();
                        const phone = document.getElementById('swal-phone').value.trim();
                        const email = document.getElementById('swal-email').value.trim();

                        if (!customerName || !phone || !email) {
                            Swal.showValidationMessage('Please fill in all required fields');
                            return false;
                        }

                        return {
                            customer_name: customerName,
                            phone: phone,
                            email: email,
                            travel_date: new Date().toISOString().split('T')[0],
                            num_passengers: 1,
                            total_amount: route.price
                        };
                    }
                });

                if (formValues) {
                    // Initialize Paystack payment
                    const handler = PaystackPop.setup({
                        key: 'pk_test_47365b50300d1d3c5d6dd9932b7cecdaa4927b3a',
                        email: formValues.email,
                        amount: Math.round(formValues.total_amount * 100), // Convert to pesewas
                        currency: 'GHS',
                        ref: 'ABC_' + Math.floor((Math.random() * 1000000000) + 1),
                        metadata: {
                            custom_fields: [
                                {
                                    display_name: "Customer Name",
                                    variable_name: "customer_name",
                                    value: formValues.customer_name
                                },
                                {
                                    display_name: "Phone Number",
                                    variable_name: "phone",
                                    value: formValues.phone
                                }
                            ]
                        },
                        onClose: function() {
                            Swal.fire({
                                icon: 'warning',
                                title: 'Payment Cancelled',
                                text: 'Your payment was cancelled. Please try again.',
                                confirmButtonColor: '#1a56db'
                            });
                        },
                        callback: function(response) {
                            // Handle the payment response
                            handlePaymentResponse(response, route, formValues);
                        }
                    });
                    handler.openIframe();
                }
            } catch (error) {
                console.error('Error processing booking:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'An error occurred while processing your request. Please try again.',
                    confirmButtonColor: '#1a56db'
                });
            }
        }

        // Function to handle payment response
        async function handlePaymentResponse(response, route, formValues) {
            try {
                // Create booking in database
                const { data: booking, error: bookingError } = await supabaseClient
                    .from('bookings')
                    .insert([{
                        route_id: route.id,
                        customer_name: formValues.customer_name,
                        email: formValues.email,
                        phone: formValues.phone,
                        num_passengers: 1,
                        travel_date: formValues.travel_date,
                        total_amount: formValues.total_amount,
                        payment_reference: response.reference,
                        payment_status: 'completed',
                        booking_status: 'confirmed'
                    }])
                    .select()
                    .single();

                if (bookingError) throw bookingError;

                // Update available seats
                const { error: updateError } = await supabaseClient
                    .from('routes')
                    .update({ 
                        available_seats: route.available_seats - 1
                    })
                    .eq('id', route.id);

                if (updateError) throw updateError;

                // Generate minimal QR code data
                const qrData = JSON.stringify({
                    bookingId: booking.id.toString(), // Ensure ID is a string
                    type: 'ticket'
                });

                // Create ticket container
                const ticketContainer = document.createElement('div');
                ticketContainer.className = 'ticket-container';
                ticketContainer.style.cssText = `
                    background: white;
                    padding: 20px;
                    width: 400px;
                    font-family: Arial;
                    border: 1px solid #e5e7eb;
                    border-radius: 8px;
                `;

                // Create header with logo
                const header = document.createElement('div');
                header.style.cssText = `
                    display: flex;
                    align-items: center;
                    margin-bottom: 20px;
                    padding-bottom: 10px;
                    border-bottom: 2px solid #1a56db;
                `;
                header.innerHTML = `
                    <img src="logo.svg" alt="ABC Travels" style="height: 40px; margin-right: 10px;">
                    <h2 style="color: #1a56db; margin: 0; font-size: 24px;">E-Ticket</h2>
                `;
                ticketContainer.appendChild(header);

                // Create QR code container
                const qrContainer = document.createElement('div');
                qrContainer.style.cssText = `
                    display: flex;
                    justify-content: center;
                    margin: 20px 0;
                    padding: 10px;
                    background: #f3f4f6;
                    border-radius: 4px;
                `;
                ticketContainer.appendChild(qrContainer);

                // Generate QR Code with better error correction
                try {
                    new QRCode(qrContainer, {
                        text: qrData,
                        width: 256, // Increased size
                        height: 256, // Increased size
                        colorDark: "#000000", // Pure black for better contrast
                        colorLight: "#ffffff", // Pure white for better contrast
                        correctLevel: QRCode.CorrectLevel.H, // Highest error correction
                        margin: 4 // Add margin for better scanning
                    });
                } catch (qrError) {
                    console.error('Error generating QR code:', qrError);
                    throw new Error('Failed to generate ticket QR code');
                }

                // Add ticket details
                const detailsContainer = document.createElement('div');
                detailsContainer.style.cssText = `
                    display: grid;
                    grid-template-columns: auto 1fr;
                    gap: 8px;
                    margin-top: 20px;
                    font-size: 14px;
                `;

                const details = [
                    ['Booking ID', booking.id],
                    ['Passenger', booking.customer_name],
                    ['Route', `${route.from_location} → ${route.to_location}`],
                    ['Date', new Date(booking.travel_date).toLocaleDateString()],
                    ['Time', route.departure_time],
                    ['Reference', booking.payment_reference]
                ];

                details.forEach(([label, value]) => {
                    detailsContainer.innerHTML += `
                        <div style="font-weight: bold; color: #4b5563;">${label}:</div>
                        <div style="color: #1f2937;">${value}</div>
                    `;
                });
                ticketContainer.appendChild(detailsContainer);

                // Add footer
                const footer = document.createElement('div');
                footer.style.cssText = `
                    margin-top: 20px;
                    padding-top: 10px;
                    border-top: 1px solid #e5e7eb;
                    text-align: center;
                    font-size: 12px;
                    color: #6b7280;
                `;
                footer.textContent = 'Present this ticket when boarding. Valid for one-time use only.';
                ticketContainer.appendChild(footer);

                // Add to document temporarily (hidden)
                ticketContainer.style.position = 'absolute';
                ticketContainer.style.left = '-9999px';
                document.body.appendChild(ticketContainer);

                // Convert to image with better quality
                const ticketImage = await html2canvas(ticketContainer, {
                    scale: 2,
                    logging: false,
                    useCORS: true,
                    backgroundColor: '#ffffff'
                });
                
                // Remove temporary elements
                document.body.removeChild(ticketContainer);

                // Handle ticket delivery
                ticketImage.toBlob(async (blob) => {
                    // Download ticket
                    const downloadLink = document.createElement('a');
                    downloadLink.href = URL.createObjectURL(blob);
                    downloadLink.download = `ABC_Travels_Ticket_${booking.id}.png`;
                    downloadLink.click();

                    // Send to server for email delivery
                    const ws = new WebSocket(currentConfig.wsUrl);
                    ws.onopen = () => {
                        console.log('Sending ticket to server for email delivery...');
                        ws.send(JSON.stringify({
                            type: 'SEND_TICKET',
                            data: {
                                email: booking.email,
                                name: booking.customer_name,
                                ticketImage: ticketImage.toDataURL('image/png', 1.0),
                                bookingDetails: {
                                    id: booking.id,
                                    route: `${route.from_location} → ${route.to_location}`,
                                    departureTime: route.departure_time,
                                    travelDate: booking.travel_date
                                }
                            }
                        }));
                    };

                    ws.onmessage = (event) => {
                        const data = JSON.parse(event.data);
                        console.log('Received response from server:', data);
                        
                        if (data.type === 'TICKET_SENT') {
                            ws.close();
                            if (data.success) {
                                console.log('Ticket sent successfully');
                                Swal.fire({
                                    icon: 'success',
                                    title: 'Booking Successful!',
                                    text: 'Your ticket has been downloaded and sent to your email.',
                                    confirmButtonColor: '#1a56db'
                                });
                            } else {
                                console.error('Failed to send ticket:', data.error);
                                Swal.fire({
                                    icon: 'warning',
                                    title: 'Booking Successful',
                                    text: 'Your ticket has been downloaded but could not be sent to your email. Please contact support.',
                                    confirmButtonColor: '#1a56db'
                                });
                            }
                        } else if (data.type === 'ERROR') {
                            ws.close();
                            console.error('Server error:', data.error);
                            Swal.fire({
                                icon: 'warning',
                                title: 'Booking Successful',
                                text: 'Your ticket has been downloaded but could not be sent to your email. Please contact support.',
                                confirmButtonColor: '#1a56db'
                            });
                        }
                    };

                    ws.onerror = (error) => {
                        console.error('WebSocket error:', error);
                        Swal.fire({
                            icon: 'warning',
                            title: 'Booking Successful',
                            text: 'Your ticket has been downloaded but could not be sent to your email. Please contact support.',
                            confirmButtonColor: '#1a56db'
                        });
                    };
                });

                // Reload routes to update availability
                loadRoutes();

            } catch (error) {
                console.error('Error processing booking:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Booking Error',
                    text: 'An error occurred while processing your booking. Please contact support.',
                    confirmButtonColor: '#1a56db'
                });
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            loadRoutes();
        });

        async function handlePaymentSuccess(bookingId) {
            try {
                // Show loading state
                Swal.fire({
                    title: 'Processing payment...',
                    text: 'Please wait while we generate your ticket.',
                    allowOutsideClick: false,
                    showConfirmButton: false,
                    willOpen: () => {
                        Swal.showLoading();
                    }
                });

                // Send request to server to process payment and send ticket
                if (ws.readyState === WebSocket.OPEN) {
                    ws.send(JSON.stringify({
                        type: 'PAYMENT_SUCCESS',
                        data: { bookingId }
                    }));
                } else {
                    throw new Error('WebSocket connection lost');
                }
            } catch (error) {
                console.error('Error handling payment success:', error);
                showError('Failed to process payment. Please contact support.');
            }
        }

        // WebSocket message handler
        ws.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                
                switch (data.type) {
                    case 'PAYMENT_PROCESSED':
                        if (data.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Payment Successful!',
                                text: 'Your ticket has been sent to your email.',
                                confirmButtonColor: '#1a56db'
                            });

                            // Download ticket if available
                            if (data.ticketUrl) {
                                const link = document.createElement('a');
                                link.href = data.ticketUrl;
                                link.download = 'ticket.pdf';
                                document.body.appendChild(link);
                                link.click();
                                document.body.removeChild(link);
                            }
                        } else {
                            showError(data.error || 'Failed to process payment');
                        }
                        break;
                }
            } catch (error) {
                console.error('Error processing WebSocket message:', error);
                showError('Failed to process server response');
            }
        };
    </script>
</body>
</html> 