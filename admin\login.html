<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - ABC Travels</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <link rel="icon" type="image/svg+xml" href="../logo.svg">
    <script src="../config.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1a56db',
                        secondary: '#1e429f',
                        accent: '#e3f2fd'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <!-- Logo and Title -->
            <div>
                <div class="flex justify-center">
                    <img src="../logo.svg" alt="ABC Travels Logo" class="h-12 w-auto">
                </div>
                <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                    Admin Login
                </h2>
                <p class="mt-2 text-center text-sm text-gray-600">
                    Access the ABC Travels admin dashboard
                </p>
            </div>

            <!-- Login Form -->
            <form id="login-form" class="mt-8 space-y-6">
                <div class="rounded-md shadow-sm -space-y-px">
                    <div>
                        <label for="username" class="sr-only">Username</label>
                        <input id="username" name="username" type="text" required class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm" placeholder="Username">
                    </div>
                    <div>
                        <label for="password" class="sr-only">Password</label>
                        <input id="password" name="password" type="password" required class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm" placeholder="Password">
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <input id="remember-me" name="remember-me" type="checkbox" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                        <label for="remember-me" class="ml-2 block text-sm text-gray-900">
                            Remember me
                        </label>
                    </div>

                    <div class="text-sm">
                        <a href="#" class="font-medium text-primary hover:text-secondary">
                            Forgot your password?
                        </a>
                    </div>
                </div>

                <div>
                    <button type="submit" class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                        <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                            <svg class="h-5 w-5 text-white group-hover:text-gray-100" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                            </svg>
                        </span>
                        Sign in
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        let ws;

        function connectWebSocket() {
            ws = new WebSocket(currentConfig.wsUrl);
            
            ws.onopen = () => {
                console.log('Connected to WebSocket server');
            };
            
            ws.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    console.log('Received WebSocket message:', data);

                    switch (data.type) {
                        case 'LOGIN_RESPONSE':
                            handleLoginResponse(data);
                            break;
                        case 'ERROR':
                            showError(data.error);
                            break;
                    }
                } catch (error) {
                    console.error('Error handling WebSocket message:', error);
                    showError('Failed to process server response');
                }
            };

            ws.onclose = () => {
                console.log('Disconnected from WebSocket server. Reconnecting...');
                setTimeout(connectWebSocket, 5000);
            };
        }

        function handleLoginResponse(data) {
            if (data.success) {
                // Store authentication data
                localStorage.setItem('adminAuthenticated', 'true');
                localStorage.setItem('adminUser', data.username);
                localStorage.setItem('adminToken', data.token);
                
                // Show success message and redirect
                Swal.fire({
                    icon: 'success',
                    title: 'Login Successful',
                    text: 'Redirecting to dashboard...',
                    timer: 1500,
                    showConfirmButton: false
                }).then(() => {
                    window.location.href = 'dashboard.html';
                });
            } else {
                showError(data.error || 'Invalid username or password');
            }
        }

        function showError(message) {
            Swal.fire({
                icon: 'error',
                title: 'Login Failed',
                text: message,
                confirmButtonColor: '#1a56db'
            });
        }

        document.getElementById('login-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('remember-me').checked;
            
            // Send login request to server
            ws.send(JSON.stringify({
                type: 'LOGIN',
                data: {
                    username,
                    password,
                    rememberMe
                }
            }));
        });

        // Check if user is already logged in
        window.addEventListener('load', function() {
            if (localStorage.getItem('adminAuthenticated') === 'true') {
                window.location.href = 'dashboard.html';
            } else {
                connectWebSocket();
            }
        });
    </script>
</body>
</html> 