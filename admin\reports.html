<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reports - ABC Travels Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.15/jspdf.plugin.autotable.min.js"></script>
    <link rel="icon" type="image/svg+xml" href="../logo.svg">
    <script src="../config.js"></script>
    <script>
        // Initialize Supabase client
        const { createClient } = supabase;
        const supabaseClient = createClient(
            currentConfig.supabaseUrl,
            currentConfig.supabaseKey
        );

        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1a56db',
                        secondary: '#1e429f',
                        accent: '#e3f2fd'
                    }
                }
            }
        }

        async function generateReport(type, startDate, endDate) {
            try {
                let query;
                switch (type) {
                    case 'bookings':
                        query = supabaseClient
                            .from('bookings')
                            .select(`
                                *,
                                routes (
                                    from_location,
                                    to_location,
                                    price
                                )
                            `)
                            .gte('created_at', startDate)
                            .lte('created_at', endDate);
                        break;
                    case 'revenue':
                        query = supabaseClient
                            .from('bookings')
                            .select(`
                                created_at,
                                payment_status,
                                total_amount,
                                routes (
                                    from_location,
                                    to_location
                                )
                            `)
                            .eq('payment_status', 'paid')
                            .gte('created_at', startDate)
                            .lte('created_at', endDate);
                        break;
                    case 'routes':
                        query = supabaseClient
                            .from('routes')
                            .select(`
                                *,
                                bookings (count)
                            `)
                            .gte('created_at', startDate)
                            .lte('created_at', endDate);
                        break;
                }

                const { data, error } = await query;
                if (error) throw error;

                // Process and display the report data
                displayReport(type, data);
                updateCharts(type, data);
            } catch (error) {
                console.error('Error generating report:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Failed to generate report',
                    confirmButtonColor: '#1a56db'
                });
            }
        }

        function displayReport(type, data) {
            const reportContent = document.getElementById('report-content');
            reportContent.innerHTML = '';

            switch (type) {
                case 'bookings':
                    displayBookingsReport(data);
                    break;
                case 'revenue':
                    displayRevenueReport(data);
                    break;
                case 'routes':
                    displayRoutesReport(data);
                    break;
                case 'tickets':
                    displayTicketsReport(data);
                    break;
            }
        }

        function displayBookingsReport(bookings) {
            const reportContent = document.getElementById('report-content');
            const table = document.createElement('table');
            table.className = 'min-w-full divide-y divide-gray-200';
            
            table.innerHTML = `
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Route</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    ${bookings.map(booking => `
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${new Date(booking.created_at).toLocaleDateString()}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                ${booking.routes.from_location} → ${booking.routes.to_location}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                ${booking.customer_name}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                    ${booking.payment_status === 'paid' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                                    ${booking.payment_status}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                GH₵ ${booking.total_amount.toFixed(2)}
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            `;
            
            reportContent.appendChild(table);
        }

        function displayRevenueReport(data) {
            const reportContent = document.getElementById('report-content');
            const totalRevenue = data.reduce((sum, booking) => sum + booking.total_amount, 0);
            
            const summary = document.createElement('div');
            summary.className = 'bg-white rounded-lg shadow p-6 mb-6';
            summary.innerHTML = `
                <h3 class="text-lg font-medium text-gray-900 mb-4">Revenue Summary</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="bg-gray-50 rounded-lg p-4">
                        <p class="text-sm text-gray-500">Total Revenue</p>
                        <p class="text-2xl font-bold text-gray-900">GH₵ ${totalRevenue.toFixed(2)}</p>
                    </div>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <p class="text-sm text-gray-500">Total Bookings</p>
                        <p class="text-2xl font-bold text-gray-900">${data.length}</p>
                    </div>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <p class="text-sm text-gray-500">Average Revenue per Booking</p>
                        <p class="text-2xl font-bold text-gray-900">GH₵ ${(totalRevenue / data.length).toFixed(2)}</p>
                    </div>
                </div>
            `;
            
            reportContent.appendChild(summary);
        }

        function displayRoutesReport(data) {
            const reportContent = document.getElementById('report-content');
            
            const table = document.createElement('table');
            table.className = 'min-w-full divide-y divide-gray-200';
            
            table.innerHTML = `
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Route</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Bookings</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Available Seats</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    ${data.map(route => `
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                ${route.from_location} → ${route.to_location}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${route.bookings.length}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${route.available_seats} / ${route.total_seats}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                    ${route.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                                    ${route.is_active ? 'Active' : 'Inactive'}
                                </span>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            `;
            
            reportContent.appendChild(table);
        }

        function displayTicketsReport(data) {
            const ctx = document.getElementById('report-chart')?.getContext('2d');
            if (!ctx) return;

            new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: ['Used', 'Unused', 'Cancelled'],
                    datasets: [{
                        data: [data.used, data.unused, data.cancelled],
                        backgroundColor: ['#1a56db', '#9ca3af', '#ef4444']
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Ticket Status Distribution'
                        }
                    }
                }
            });

            // Create table with resend button
            const table = document.createElement('table');
            table.className = 'min-w-full divide-y divide-gray-200';

            // Create header
            const thead = document.createElement('thead');
            thead.className = 'bg-gray-50';
            const headerRow = document.createElement('tr');
            ['Booking ID', 'Customer', 'Route', 'Date', 'Status', 'Actions'].forEach(header => {
                const th = document.createElement('th');
                th.className = 'px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider';
                th.textContent = header;
                headerRow.appendChild(th);
            });
            thead.appendChild(headerRow);
            table.appendChild(thead);

            // Create body
            const tbody = document.createElement('tbody');
            tbody.className = 'bg-white divide-y divide-gray-200';
            data.bookings.forEach(booking => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${booking.id}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${booking.customer_name}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${booking.route}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${new Date(booking.date).toLocaleDateString()}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${booking.status}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${booking.status === 'completed' ? `
                            <button onclick="resendTicket('${booking.id}')" 
                                class="text-primary hover:text-secondary focus:outline-none">
                                Resend Ticket
                            </button>
                        ` : '-'}
                    </td>
                `;
                tbody.appendChild(tr);
            });
            table.appendChild(tbody);

            const tableContainer = document.getElementById('table-container');
            if (tableContainer) {
                tableContainer.innerHTML = '';
                tableContainer.appendChild(table);
            }
        }

        async function resendTicket(bookingId) {
            try {
                // Show loading state
                Swal.fire({
                    title: 'Resending Ticket',
                    text: 'Please wait...',
                    allowOutsideClick: false,
                    showConfirmButton: false,
                    willOpen: () => {
                        Swal.showLoading();
                    }
                });

                // Send request to server
                if (ws.readyState === WebSocket.OPEN) {
                    ws.send(JSON.stringify({
                        type: 'RESEND_TICKET',
                        data: { bookingId }
                    }));
                } else {
                    throw new Error('WebSocket connection lost');
                }
            } catch (error) {
                console.error('Error resending ticket:', error);
                showError('Failed to resend ticket. Please try again.');
            }
        }

        function updateCharts(type, data) {
            const ctx = document.getElementById('report-chart').getContext('2d');
            
            // Destroy existing chart if it exists
            if (window.reportChart) {
                window.reportChart.destroy();
            }

            let chartData;
            switch (type) {
                case 'bookings':
                    chartData = processBookingsChartData(data);
                    break;
                case 'revenue':
                    chartData = processRevenueChartData(data);
                    break;
                case 'routes':
                    chartData = processRoutesChartData(data);
                    break;
            }

            window.reportChart = new Chart(ctx, chartData);
        }

        function processBookingsChartData(data) {
            const dailyBookings = {};
            data.forEach(booking => {
                const date = new Date(booking.created_at).toLocaleDateString();
                dailyBookings[date] = (dailyBookings[date] || 0) + 1;
            });

            return {
                type: 'line',
                data: {
                    labels: Object.keys(dailyBookings),
                    datasets: [{
                        label: 'Daily Bookings',
                        data: Object.values(dailyBookings),
                        borderColor: '#1a56db',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Bookings Over Time'
                        }
                    }
                }
            };
        }

        function processRevenueChartData(data) {
            const dailyRevenue = {};
            data.forEach(booking => {
                const date = new Date(booking.created_at).toLocaleDateString();
                dailyRevenue[date] = (dailyRevenue[date] || 0) + booking.total_amount;
            });

            return {
                type: 'bar',
                data: {
                    labels: Object.keys(dailyRevenue),
                    datasets: [{
                        label: 'Daily Revenue (GH₵)',
                        data: Object.values(dailyRevenue),
                        backgroundColor: '#1a56db'
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Revenue Over Time'
                        }
                    }
                }
            };
        }

        function processRoutesChartData(data) {
            return {
                type: 'pie',
                data: {
                    labels: data.map(route => `${route.from_location} → ${route.to_location}`),
                    datasets: [{
                        data: data.map(route => route.bookings.length),
                        backgroundColor: [
                            '#1a56db',
                            '#1e429f',
                            '#e3f2fd',
                            '#60a5fa',
                            '#3b82f6'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Bookings by Route'
                        }
                    }
                }
            };
        }

        function loadReport(type) {
            document.getElementById('report-type').value = type;
            generateReport();
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Set default dates
            setDefaultDates();
            
            // Add event listeners
            const reportTypeSelect = document.getElementById('report-type');
            if (reportTypeSelect) {
                reportTypeSelect.addEventListener('change', function() {
                    generateReport();
                });
            }

            // Connect WebSocket
            connectWebSocket();
        });

        function generateReport() {
            const reportType = document.getElementById('report-type')?.value || 'bookings';
            const startDate = document.getElementById('start-date')?.value;
            const endDate = document.getElementById('end-date')?.value;

            if (!startDate || !endDate) {
                setDefaultDates();
                return;
            }

            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({
                    type: 'GET_REPORT',
                    data: {
                        reportType,
                        startDate,
                        endDate
                    }
                }));
            }
        }
    </script>
</head>
<body class="bg-gray-100">
    <!-- Check Authentication -->
    <script>
        if (localStorage.getItem('adminAuthenticated') !== 'true') {
            window.location.href = 'login.html';
        }
    </script>

    <!-- Mobile Menu Button -->
    <div class="fixed top-0 left-0 z-50 p-4 md:hidden">
        <button id="mobile-menu-button" class="text-gray-500 hover:text-gray-600 focus:outline-none">
            <svg class="h-6 w-6 menu-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
            </svg>
        </button>
    </div>

    <!-- Overlay -->
    <div id="overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden md:hidden"></div>

    <!-- Admin Layout -->
    <div class="min-h-screen flex">
        <!-- Sidebar -->
        <div id="sidebar" class="fixed inset-y-0 left-0 transform -translate-x-full md:relative md:translate-x-0 transition-transform duration-300 ease-in-out bg-gray-800 text-white w-64 py-6 flex flex-col z-50">
            <!-- Sidebar content -->
            <div class="px-6 mb-8">
                <div class="flex items-center">
                    <img src="../logo.svg" alt="ABC Travels Logo" class="h-8 w-auto filter invert">
                    <span class="ml-2 text-lg font-bold">Admin Panel</span>
                </div>
            </div>
            <nav class="flex-1">
                <a href="dashboard.html" class="flex items-center px-6 py-3 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200">
                    <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
                    </svg>
                    Dashboard
                </a>
                <a href="routes.html" class="flex items-center px-6 py-3 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200">
                    <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"/>
                    </svg>
                    Routes
                </a>
                <a href="bookings.html" class="flex items-center px-6 py-3 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200">
                    <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 4v12l-4-2-4 2V4M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                    </svg>
                    Bookings
                </a>
                <a href="scanner.html" class="flex items-center px-6 py-3 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200">
                    <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"/>
                    </svg>
                    QR Scanner
                </a>
                <a href="reports.html" class="flex items-center px-6 py-3 bg-gray-900 text-white">
                    <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                    </svg>
                    Reports
                </a>
            </nav>
            <div class="px-6 pt-4 pb-2 border-t border-gray-700">
                <button onclick="logout()" class="flex items-center px-4 py-2 text-gray-300 hover:text-white transition-colors duration-200">
                    <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
                    </svg>
                    Logout
                </button>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1">
            <header class="bg-white shadow-sm">
                <div class="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
                    <h1 class="text-lg font-semibold text-gray-900">Reports</h1>
                </div>
            </header>

            <main class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                <!-- Date Range Filter -->
                <div class="bg-white rounded-lg shadow p-6 mb-6">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label for="report-type" class="block text-sm font-medium text-gray-700">Report Type</label>
                            <select id="report-type" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary">
                                <option value="bookings">Bookings</option>
                                <option value="revenue">Revenue</option>
                                <option value="routes">Routes</option>
                                <option value="tickets">Tickets</option>
                            </select>
                        </div>
                        <div>
                            <label for="start-date" class="block text-sm font-medium text-gray-700">Start Date</label>
                            <input type="date" id="start-date" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary">
                        </div>
                        <div>
                            <label for="end-date" class="block text-sm font-medium text-gray-700">End Date</label>
                            <input type="date" id="end-date" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary">
                        </div>
                        <div class="flex items-end">
                            <button onclick="generateReport()" class="w-full bg-primary text-white px-4 py-2 rounded-md hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2">
                                Generate Report
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Report Content -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Revenue Chart -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <h2 class="text-lg font-medium text-gray-900 mb-4">Revenue Overview</h2>
                        <canvas id="revenueChart" class="w-full" height="300"></canvas>
                    </div>

                    <!-- Bookings Chart -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <h2 class="text-lg font-medium text-gray-900 mb-4">Bookings Overview</h2>
                        <canvas id="bookingsChart" class="w-full" height="300"></canvas>
                    </div>

                    <!-- Popular Routes -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <h2 class="text-lg font-medium text-gray-900 mb-4">Popular Routes</h2>
                        <div id="popular-routes" class="space-y-4">
                            <!-- Popular routes will be inserted here -->
                        </div>
                    </div>

                    <!-- Summary Stats -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <h2 class="text-lg font-medium text-gray-900 mb-4">Summary</h2>
                        <div class="space-y-4">
                            <div class="flex justify-between items-center p-4 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="text-sm font-medium text-gray-500">Total Revenue</p>
                                    <p id="total-revenue" class="text-2xl font-semibold text-gray-900">GH₵ 0</p>
                                </div>
                                <svg class="h-8 w-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            </div>
                            <div class="flex justify-between items-center p-4 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="text-sm font-medium text-gray-500">Total Bookings</p>
                                    <p id="total-bookings" class="text-2xl font-semibold text-gray-900">0</p>
                                </div>
                                <svg class="h-8 w-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 4v12l-4-2-4 2V4M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                </svg>
                            </div>
                            <div class="flex justify-between items-center p-4 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="text-sm font-medium text-gray-500">Average Daily Revenue</p>
                                    <p id="avg-daily-revenue" class="text-2xl font-semibold text-gray-900">GH₵ 0</p>
                                </div>
                                <svg class="h-8 w-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        let ws;
        let revenueChart;
        let bookingsChart;
        let currentReportData = null;

        // Check authentication
        if (!localStorage.getItem('adminAuthenticated')) {
            window.location.href = 'login.html';
        }

        function connectWebSocket() {
            ws = new WebSocket(currentConfig.wsUrl);
            
            ws.onopen = () => {
                console.log('Connected to WebSocket server');
                // Set default dates and generate initial report
                setDefaultDates();
                generateReport();
            };
            
            ws.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    
                    switch (data.type) {
                        case 'REPORT_DATA':
                            handleReportData(data.data);
                            break;
                        case 'ERROR':
                            showError(data.error);
                            break;
                        case 'TICKET_RESEND_RESULT':
                            if (data.success) {
                                Swal.fire({
                                    icon: 'success',
                                    title: 'Success',
                                    text: 'Ticket has been resent successfully!',
                                    confirmButtonColor: '#1a56db'
                                });
                            } else {
                                showError(data.error || 'Failed to resend ticket');
                            }
                            break;
                    }
                } catch (error) {
                    console.error('Error handling WebSocket message:', error);
                    showError('Failed to process server response');
                }
            };

            ws.onclose = () => {
                console.log('Disconnected from WebSocket server');
                setTimeout(connectWebSocket, 5000);
            };
        }

        function setDefaultDates() {
            const today = new Date();
            const thirtyDaysAgo = new Date(today);
            thirtyDaysAgo.setDate(today.getDate() - 30);
            
            document.getElementById('start-date').value = thirtyDaysAgo.toISOString().split('T')[0];
            document.getElementById('end-date').value = today.toISOString().split('T')[0];
        }

        function handleReportData(data) {
            currentReportData = data;
            updateCharts(data);
            updateTable(data);
        }

        function updateCharts(data) {
            // Destroy existing charts
            if (revenueChart) revenueChart.destroy();
            if (bookingsChart) bookingsChart.destroy();

            // Create new charts based on report type
            const reportType = document.getElementById('report-type').value;
            
            if (reportType === 'revenue' || reportType === 'bookings') {
                // Revenue Chart
                const revenueCtx = document.getElementById('revenueChart').getContext('2d');
                revenueChart = new Chart(revenueCtx, {
                    type: 'line',
                    data: {
                        labels: data.dates,
                        datasets: [{
                            label: 'Revenue (GHS)',
                            data: data.revenue,
                            borderColor: '#1a56db',
                            tension: 0.1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false
                    }
                });

                // Bookings Chart
                const bookingsCtx = document.getElementById('bookingsChart').getContext('2d');
                bookingsChart = new Chart(bookingsCtx, {
                    type: 'bar',
                    data: {
                        labels: data.dates,
                        datasets: [{
                            label: 'Number of Bookings',
                            data: data.bookings,
                            backgroundColor: '#1e429f'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false
                    }
                });
            } else if (reportType === 'routes') {
                // Routes Performance Chart
                const revenueCtx = document.getElementById('revenueChart').getContext('2d');
                revenueChart = new Chart(revenueCtx, {
                    type: 'bar',
                    data: {
                        labels: data.routes,
                        datasets: [{
                            label: 'Revenue per Route (GHS)',
                            data: data.routeRevenue,
                            backgroundColor: '#1a56db'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false
                    }
                });

                // Route Usage Chart
                const bookingsCtx = document.getElementById('bookingsChart').getContext('2d');
                bookingsChart = new Chart(bookingsCtx, {
                    type: 'pie',
                    data: {
                        labels: data.routes,
                        datasets: [{
                            data: data.routeUsage,
                            backgroundColor: [
                                '#1a56db',
                                '#1e429f',
                                '#2563eb',
                                '#3b82f6',
                                '#60a5fa'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false
                    }
                });
            }
        }

        function updateTable(data) {
            const headers = document.getElementById('table-headers');
            const body = document.getElementById('table-body');
            const reportType = document.getElementById('report-type').value;

            // Clear existing content
            headers.innerHTML = '';
            body.innerHTML = '';

            // Set headers based on report type
            let headerRow = '';
            switch (reportType) {
                case 'revenue':
                    headerRow = `
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Revenue (GHS)</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bookings</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg. Ticket Price</th>
                    `;
                    break;
                case 'routes':
                    headerRow = `
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Route</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Bookings</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Revenue (GHS)</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Usage %</th>
                    `;
                    break;
                case 'tickets':
                    headerRow = `
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ticket ID</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Route</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Scan Date</th>
                    `;
                    break;
            }
            headers.innerHTML = headerRow;

            // Populate table data
            data.tableData.forEach(row => {
                const tr = document.createElement('tr');
                tr.innerHTML = row.map(cell => `<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${cell}</td>`).join('');
                body.appendChild(tr);
            });
        }

        function exportToPDF() {
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF();
            
            // Add title
            const reportType = document.getElementById('report-type').value.toUpperCase();
            const startDate = document.getElementById('start-date').value;
            const endDate = document.getElementById('end-date').value;
            
            doc.setFontSize(16);
            doc.text(`ABC TRAVELS - ${reportType} REPORT`, 14, 15);
            doc.setFontSize(12);
            doc.text(`Period: ${startDate} to ${endDate}`, 14, 25);

            // Add table
            doc.autoTable({
                head: [currentReportData.headers],
                body: currentReportData.tableData,
                startY: 35,
                theme: 'grid'
            });

            // Save PDF
            doc.save(`ABC_Travels_${reportType}_Report_${startDate}_${endDate}.pdf`);
        }

        function exportToCSV() {
            const reportType = document.getElementById('report-type').value;
            const startDate = document.getElementById('start-date').value;
            const endDate = document.getElementById('end-date').value;
            
            // Convert data to CSV
            const headers = currentReportData.headers.join(',');
            const rows = currentReportData.tableData.map(row => row.join(','));
            const csv = [headers, ...rows].join('\n');

            // Create download link
            const blob = new Blob([csv], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.setAttribute('hidden', '');
            a.setAttribute('href', url);
            a.setAttribute('download', `ABC_Travels_${reportType}_Report_${startDate}_${endDate}.csv`);
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
        }

        // Event Listeners
        const generateBtn = document.getElementById('generate-btn');
        const exportPdfBtn = document.getElementById('export-pdf');
        const exportCsvBtn = document.getElementById('export-csv');
        const logoutBtn = document.getElementById('logout-btn');

        if (generateBtn) generateBtn.addEventListener('click', generateReport);
        if (exportPdfBtn) exportPdfBtn.addEventListener('click', exportToPDF);
        if (exportCsvBtn) exportCsvBtn.addEventListener('click', exportToCSV);
        if (logoutBtn) logoutBtn.addEventListener('click', () => {
            localStorage.clear();
            window.location.href = 'login.html';
        });

        // Initialize WebSocket connection
        connectWebSocket();
    </script>
</body>
</html> 