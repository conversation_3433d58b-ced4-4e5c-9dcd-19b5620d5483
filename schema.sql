-- Create routes table
CREATE TABLE routes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    from_location VARCHAR(100) NOT NULL,
    to_location VARCHAR(100) NOT NULL,
    departure_time TIME NOT NULL,
    distance_km DECIMAL(10,2) NOT NULL, -- distance in kilometers
    price DECIMAL(10,2) NOT NULL,
    available_seats INTEGER NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create bookings table
CREATE TABLE bookings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    route_id UUID REFERENCES routes(id),
    customer_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    num_passengers INTEGER NOT NULL,
    travel_date DATE NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    payment_reference VARCHAR(100),
    payment_status VARCHAR(20) NOT NULL, -- pending, completed, failed
    booking_status VARCHAR(20) NOT NULL, -- pending, confirmed, cancelled, scanned
    special_requests TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    ticket_image TEXT
);

-- Create scans table
CREATE TABLE scans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    booking_id UUID REFERENCES bookings(id),
    scanned_by VARCHAR(100) NOT NULL,
    location VARCHAR(100) NOT NULL,
    scan_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    scan_status VARCHAR(20) NOT NULL, -- 'valid', 'invalid', 'duplicate'
    scan_message TEXT,
    device_info TEXT,
    is_first_scan BOOLEAN DEFAULT true
);

-- Create admins table
CREATE TABLE admins (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create ticket_emails table
CREATE TABLE ticket_emails (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    booking_id UUID REFERENCES bookings(id),
    sent_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(20) NOT NULL, -- success, failed
    error_message TEXT,
    recipient_email VARCHAR(255) NOT NULL
);

-- Create locations table
CREATE TABLE locations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create trigger for locations updated_at
CREATE TRIGGER update_locations_updated_at
    BEFORE UPDATE ON locations
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create index for locations search
CREATE INDEX idx_locations_name ON locations(name);

-- Insert default locations
INSERT INTO locations (name) VALUES
    ('Accra'),
    ('Kumasi'),
    ('Takoradi'),
    ('Tamale')
ON CONFLICT (name) DO NOTHING;

-- Create triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_routes_updated_at
    BEFORE UPDATE ON routes
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bookings_updated_at
    BEFORE UPDATE ON bookings
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create indexes for better query performance
CREATE INDEX idx_routes_locations ON routes(from_location, to_location);
CREATE INDEX idx_routes_active ON routes(is_active);
CREATE INDEX idx_bookings_route ON bookings(route_id);
CREATE INDEX idx_bookings_date ON bookings(travel_date);
CREATE INDEX idx_bookings_status ON bookings(booking_status, payment_status);
CREATE INDEX idx_bookings_customer ON bookings(customer_name, email);
CREATE INDEX idx_ticket_emails_booking ON ticket_emails(booking_id);

-- Create view for revenue reports
CREATE VIEW revenue_overview AS
SELECT 
    DATE_TRUNC('day', created_at) AS date,
    COUNT(*) as total_bookings,
    SUM(total_amount) as total_revenue,
    COUNT(DISTINCT route_id) as routes_used
FROM bookings 
WHERE payment_status = 'completed'
GROUP BY DATE_TRUNC('day', created_at)
ORDER BY date DESC;

-- Create view for booking statistics
CREATE VIEW booking_overview AS
SELECT 
    routes.from_location,
    routes.to_location,
    COUNT(*) as total_bookings,
    SUM(bookings.num_passengers) as total_passengers,
    SUM(bookings.total_amount) as total_revenue
FROM bookings
JOIN routes ON bookings.route_id = routes.id
WHERE bookings.payment_status = 'completed'
GROUP BY routes.from_location, routes.to_location;

-- Create view for email statistics
CREATE VIEW email_stats AS
SELECT 
    booking_id,
    COUNT(*) as total_sends,
    SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as successful_sends,
    MAX(sent_at) as last_sent_at
FROM ticket_emails
GROUP BY booking_id;

-- Create view for detailed revenue analytics
CREATE OR REPLACE VIEW revenue_analytics AS
WITH daily_revenue AS (
    SELECT 
        DATE_TRUNC('day', created_at) AS date,
        SUM(total_amount) as daily_revenue,
        COUNT(*) as bookings_count
    FROM bookings 
    WHERE payment_status = 'completed'
    GROUP BY DATE_TRUNC('day', created_at)
),
monthly_revenue AS (
    SELECT 
        DATE_TRUNC('month', created_at) AS month,
        EXTRACT(YEAR FROM created_at) AS year,
        SUM(total_amount) as monthly_revenue,
        COUNT(*) as monthly_bookings
    FROM bookings 
    WHERE payment_status = 'completed'
    GROUP BY DATE_TRUNC('month', created_at), EXTRACT(YEAR FROM created_at)
),
route_revenue AS (
    SELECT 
        r.id as route_id,
        r.from_location,
        r.to_location,
        SUM(b.total_amount) as total_revenue,
        COUNT(*) as total_bookings,
        SUM(b.total_amount) / COUNT(*) as avg_revenue_per_booking,
        SUM(b.total_amount) * 100.0 / (SELECT SUM(total_amount) FROM bookings WHERE payment_status = 'completed') as percentage_of_total
    FROM bookings b
    JOIN routes r ON b.route_id = r.id
    WHERE b.payment_status = 'completed'
    GROUP BY r.id, r.from_location, r.to_location
)
SELECT 
    'all_time' as period,
    (SELECT SUM(total_amount) FROM bookings WHERE payment_status = 'completed') as total_revenue,
    (SELECT MAX(daily_revenue) FROM daily_revenue) as highest_revenue,
    (SELECT date FROM daily_revenue ORDER BY daily_revenue DESC LIMIT 1) as highest_revenue_date,
    (SELECT AVG(daily_revenue) FROM daily_revenue) as avg_daily_revenue,
    (SELECT SUM(total_amount) / COUNT(*) FROM bookings WHERE payment_status = 'completed') as avg_revenue_per_booking,
    json_agg(json_build_object(
        'route_id', rr.route_id,
        'from_location', rr.from_location,
        'to_location', rr.to_location,
        'total_revenue', rr.total_revenue,
        'total_bookings', rr.total_bookings,
        'avg_revenue_per_booking', rr.avg_revenue_per_booking,
        'percentage_of_total', rr.percentage_of_total
    )) as revenue_by_route,
    json_agg(json_build_object(
        'month', mr.month,
        'year', mr.year,
        'revenue', mr.monthly_revenue,
        'bookings', mr.monthly_bookings
    )) as monthly_comparison
FROM route_revenue rr
CROSS JOIN monthly_revenue mr;

-- Create function to get revenue analytics for a specific period
CREATE OR REPLACE FUNCTION get_revenue_analytics(period_days INTEGER)
RETURNS TABLE (
    period TEXT,
    total_revenue DECIMAL,
    highest_revenue DECIMAL,
    highest_revenue_date TIMESTAMP WITH TIME ZONE,
    avg_daily_revenue DECIMAL,
    avg_revenue_per_booking DECIMAL,
    revenue_by_route JSON,
    monthly_comparison JSON
) AS $$
BEGIN
    RETURN QUERY
    WITH filtered_bookings AS (
        SELECT *
        FROM bookings
        WHERE payment_status = 'completed'
        AND created_at >= CURRENT_DATE - (period_days || ' days')::INTERVAL
    ),
    daily_revenue AS (
        SELECT 
            DATE_TRUNC('day', created_at) AS date,
            SUM(total_amount) as daily_revenue,
            COUNT(*) as bookings_count
        FROM filtered_bookings
        GROUP BY DATE_TRUNC('day', created_at)
    ),
    monthly_revenue AS (
        SELECT 
            DATE_TRUNC('month', created_at) AS month,
            EXTRACT(YEAR FROM created_at) AS year,
            SUM(total_amount) as monthly_revenue,
            COUNT(*) as monthly_bookings
        FROM filtered_bookings
        GROUP BY DATE_TRUNC('month', created_at), EXTRACT(YEAR FROM created_at)
    ),
    route_revenue AS (
        SELECT 
            r.id as route_id,
            r.from_location,
            r.to_location,
            SUM(b.total_amount) as total_revenue,
            COUNT(*) as total_bookings,
            SUM(b.total_amount) / COUNT(*) as avg_revenue_per_booking,
            SUM(b.total_amount) * 100.0 / (SELECT SUM(total_amount) FROM filtered_bookings) as percentage_of_total
        FROM filtered_bookings b
        JOIN routes r ON b.route_id = r.id
        GROUP BY r.id, r.from_location, r.to_location
    )
    SELECT 
        period_days || '_days' as period,
        (SELECT SUM(total_amount) FROM filtered_bookings) as total_revenue,
        (SELECT MAX(daily_revenue) FROM daily_revenue) as highest_revenue,
        (SELECT date FROM daily_revenue ORDER BY daily_revenue DESC LIMIT 1) as highest_revenue_date,
        (SELECT AVG(daily_revenue) FROM daily_revenue) as avg_daily_revenue,
        (SELECT SUM(total_amount) / COUNT(*) FROM filtered_bookings) as avg_revenue_per_booking,
        (SELECT json_agg(json_build_object(
            'route_id', route_id,
            'from_location', from_location,
            'to_location', to_location,
            'total_revenue', total_revenue,
            'total_bookings', total_bookings,
            'avg_revenue_per_booking', avg_revenue_per_booking,
            'percentage_of_total', percentage_of_total
        )) FROM route_revenue) as revenue_by_route,
        (SELECT json_agg(json_build_object(
            'month', month,
            'year', year,
            'revenue', monthly_revenue,
            'bookings', monthly_bookings
        )) FROM monthly_revenue) as monthly_comparison;
END;
$$ LANGUAGE plpgsql;

-- Create indexes to optimize revenue queries
CREATE INDEX idx_bookings_created_at ON bookings(created_at);
CREATE INDEX idx_bookings_payment_status_created_at ON bookings(payment_status, created_at);

-- Create index for efficient scan queries
CREATE INDEX idx_scans_booking ON scans(booking_id);
CREATE INDEX idx_scans_time ON scans(scan_time);
CREATE INDEX idx_scans_status ON scans(scan_status);

-- Create function to check if ticket is valid and not already used
CREATE OR REPLACE FUNCTION check_ticket_validity(p_booking_id UUID)
RETURNS TABLE (
    is_valid BOOLEAN,
    message TEXT,
    booking_data JSONB
) AS $$
DECLARE
    v_booking_exists BOOLEAN;
    v_is_first_scan BOOLEAN;
    v_travel_date DATE;
    v_booking_status VARCHAR;
    v_payment_status VARCHAR;
    v_booking_data JSONB;
BEGIN
    -- Check if booking exists and get relevant data
    SELECT 
        EXISTS (
            SELECT 1 
            FROM bookings 
            WHERE id = p_booking_id
        ),
        b.travel_date,
        b.booking_status,
        b.payment_status
    INTO 
        v_booking_exists,
        v_travel_date,
        v_booking_status,
        v_payment_status
    FROM bookings b
    WHERE b.id = p_booking_id;

    IF NOT v_booking_exists THEN
        RETURN QUERY SELECT 
            FALSE,
            'Invalid ticket: Booking not found',
            NULL::JSONB;
        RETURN;
    END IF;

    -- Check payment status
    IF v_payment_status != 'completed' THEN
        RETURN QUERY SELECT 
            FALSE,
            'Invalid ticket: Payment not completed',
            NULL::JSONB;
        RETURN;
    END IF;

    -- Check booking status
    IF v_booking_status = 'cancelled' THEN
        RETURN QUERY SELECT 
            FALSE,
            'Invalid ticket: Booking has been cancelled',
            NULL::JSONB;
        RETURN;
    END IF;

    IF v_booking_status = 'used' THEN
        RETURN QUERY SELECT 
            FALSE,
            'Invalid ticket: Already used',
            NULL::JSONB;
        RETURN;
    END IF;

    -- Check if ticket has been scanned before
    SELECT NOT EXISTS (
        SELECT 1 FROM scans 
        WHERE booking_id = p_booking_id 
        AND scan_status = 'valid'
    ) INTO v_is_first_scan;

    -- Get booking data for response
    SELECT jsonb_build_object(
        'booking', jsonb_build_object(
            'id', b.id,
            'customer_name', b.customer_name,
            'travel_date', b.travel_date,
            'num_passengers', b.num_passengers,
            'payment_status', b.payment_status,
            'booking_status', b.booking_status
        ),
        'route', jsonb_build_object(
            'from_location', r.from_location,
            'to_location', r.to_location,
            'departure_time', r.departure_time
        )
    )
    FROM bookings b
    JOIN routes r ON b.route_id = r.id
    WHERE b.id = p_booking_id
    INTO v_booking_data;

    -- Validate ticket based on travel date
    IF v_travel_date < CURRENT_DATE THEN
        RETURN QUERY SELECT 
            FALSE,
            'Invalid ticket: Travel date has passed',
            v_booking_data;
    ELSIF v_travel_date > CURRENT_DATE THEN
        RETURN QUERY SELECT 
            FALSE,
            'Invalid ticket: Travel date is in the future',
            v_booking_data;
    ELSIF NOT v_is_first_scan THEN
        RETURN QUERY SELECT 
            FALSE,
            'Invalid ticket: Already scanned',
            v_booking_data;
    ELSE
        RETURN QUERY SELECT 
            TRUE,
            'Valid ticket',
            v_booking_data;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update booking status when scanned
CREATE OR REPLACE FUNCTION update_booking_status_on_scan()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.scan_status = 'valid' THEN
        UPDATE bookings 
        SET booking_status = 'scanned',
            updated_at = CURRENT_TIMESTAMP
        WHERE id = NEW.booking_id;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_booking_status_trigger
    AFTER INSERT ON scans
    FOR EACH ROW
    EXECUTE FUNCTION update_booking_status_on_scan(); 