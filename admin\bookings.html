<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bookings - ABC Travels Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <link rel="icon" type="image/svg+xml" href="../logo.svg">
    <script src="../config.js"></script>
    <script>
        // Initialize Supabase client
        const { createClient } = supabase;
        const supabaseClient = createClient(
            currentConfig.supabaseUrl,
            currentConfig.supabaseKey
        );

        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1a56db',
                        secondary: '#1e429f',
                        accent: '#e3f2fd'
                    }
                }
            }
        }
    </script>
    <style>
        .sidebar-transition {
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .menu-icon {
            transition: transform 0.3s ease;
        }
        
        .menu-icon.active {
            transform: rotate(90deg);
        }
        
        .overlay {
            background-color: rgba(0, 0, 0, 0.5);
            transition: opacity 0.3s ease;
        }
        td.px-6.py-4.whitespace-nowrap.text-sm.text-gray-900 {
    max-width: 11rem;
    overflow: hidden;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Check Authentication -->
    <script>
        if (localStorage.getItem('adminAuthenticated') !== 'true') {
            window.location.href = 'login.html';
        }
    </script>

    <!-- Mobile Menu Button -->
    <div class="fixed top-0 left-0 z-50 p-4 md:hidden">
        <button id="mobile-menu-button" class="text-gray-500 hover:text-gray-600 focus:outline-none">
            <svg class="h-6 w-6 menu-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
            </svg>
        </button>
    </div>

    <!-- Overlay -->
    <div id="overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden md:hidden"></div>

    <!-- Admin Layout -->
    <div class="min-h-screen flex">
        <!-- Sidebar -->
        <div id="sidebar" class="fixed inset-y-0 left-0 transform -translate-x-full md:relative md:translate-x-0 transition-transform duration-300 ease-in-out bg-gray-800 text-white w-64 py-6 flex flex-col z-50">
            <div class="px-6 mb-8">
                <div class="flex items-center">
                    <img src="../logo.svg" alt="ABC Travels Logo" class="h-8 w-auto filter invert">
                </div>
            </div>
            <nav class="flex-1">
                <a href="dashboard.html" class="flex items-center px-6 py-3 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200">
                    <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
                    </svg>
                    Dashboard
                </a>
                <a href="routes.html" class="flex items-center px-6 py-3 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200">
                    <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"/>
                    </svg>
                    Routes
                </a>
                <a href="bookings.html" class="flex items-center px-6 py-3 bg-gray-900 text-white">
                    <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 4v12l-4-2-4 2V4M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                    </svg>
                    Bookings
                </a>
                <a href="scanner.html" class="flex items-center px-6 py-3 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200">
                    <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"/>
                    </svg>
                    QR Scanner
                </a>
                <!-- <a href="reports.html" class="flex items-center px-6 py-3 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200">
                    <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                    </svg>
                    Reports
                </a> -->
            </nav>
            <div class="px-6 pt-4 pb-2 border-t border-gray-700">
                <button onclick="logout()" class="flex items-center px-4 py-2 text-gray-300 hover:text-white transition-colors duration-200">
                    <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
                    </svg>
                    Logout
                </button>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1">
            <header class="bg-white shadow-sm">
                <div class="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
                    <h1 class="text-lg font-semibold text-gray-900">Bookings</h1>
                </div>
            </header>

            <main class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                <!-- Filters -->
                <div class="bg-white rounded-lg shadow p-6 mb-6">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label for="date-filter" class="block text-sm font-medium text-gray-700">Date</label>
                            <input type="date" id="date-filter" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary">
                        </div>
                        <div>
                            <label for="route-filter" class="block text-sm font-medium text-gray-700">Route</label>
                            <select id="route-filter" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary">
                                <option value="">All Routes</option>
                            </select>
                        </div>
                        <div>
                            <label for="status-filter" class="block text-sm font-medium text-gray-700">Status</label>
                            <select id="status-filter" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary">
                                <option value="">All Status</option>
                                <option value="confirmed">Confirmed</option>
                                <option value="scanned">Scanned</option>
                            </select>
                        </div>
                        <div class="flex items-end">
                            <button onclick="applyFilters()" class="w-full bg-primary text-white px-4 py-2 rounded-md hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2">
                                Apply Filters
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Bookings Table -->
                <div class="bg-white rounded-lg shadow overflow-hidden">
                    <div class="min-w-full divide-y divide-gray-200">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Booking ID
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Customer
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Route
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Date
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Amount
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Status
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody id="bookings-table-body" class="bg-white divide-y divide-gray-200">
                                <!-- Bookings will be inserted here -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Pagination -->
                <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                    <div class="flex-1 flex justify-between sm:hidden">
                        <button onclick="previousPage()" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            Previous
                        </button>
                        <button onclick="nextPage()" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            Next
                        </button>
                    </div>
                    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-gray-700">
                                Showing <span id="page-start">0</span> to <span id="page-end">0</span> of <span id="total-items">0</span> results
                            </p>
                        </div>
                        <div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                <button onclick="previousPage()" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    Previous
                                </button>
                                <button onclick="nextPage()" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    Next
                                </button>
                            </nav>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        // Initialize variables
        let ws = null;
        let currentPage = 1;
        const itemsPerPage = 10;
        let isSidebarOpen = false;

        // Initialize mobile menu
        function initializeMobileMenu() {
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('overlay');
            const menuIcon = document.querySelector('.menu-icon');

            mobileMenuButton.addEventListener('click', () => {
                isSidebarOpen = !isSidebarOpen;
                if (isSidebarOpen) {
                    sidebar.classList.remove('-translate-x-full');
                    overlay.classList.remove('hidden');
                    menuIcon.classList.add('active');
                } else {
                    sidebar.classList.add('-translate-x-full');
                    overlay.classList.add('hidden');
                    menuIcon.classList.remove('active');
                }
            });

            overlay.addEventListener('click', () => {
                isSidebarOpen = false;
                sidebar.classList.add('-translate-x-full');
                overlay.classList.add('hidden');
                menuIcon.classList.remove('active');
            });
        }

        // WebSocket connection
        function connectWebSocket() {
            ws = new WebSocket(currentConfig.wsUrl);
            
            ws.onopen = () => {
                console.log('Connected to WebSocket server');
                loadRoutes();
                requestBookingsUpdate();
            };
            
            ws.onclose = () => {
                console.log('WebSocket connection closed');
                setTimeout(connectWebSocket, 5000);
            };
            
            ws.onerror = (error) => {
                console.error('WebSocket error:', error);
            };
            
            ws.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    console.log('Received message:', data);
                    
                    switch (data.type.toLowerCase()) {
                        case 'bookings':
                            updateBookingsTable(data.bookings);
                            updatePagination(data.total, data.page, data.itemsPerPage);
                            break;
                        case 'routes_data':
                            updateRouteFilter(data.data);
                            break;
                        case 'ticket_resend_result':
                            handleTicketResendResult(data);
                            break;
                        case 'error':
                            showError(data.message || data.error);
                            break;
                    }
                } catch (error) {
                    console.error('Error handling message:', error);
                }
            };
        }

        // Load routes for filter
        function loadRoutes() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({
                    type: 'GET_ROUTES'
                }));
            }
        }

        // Update route filter dropdown
        function updateRouteFilter(routes) {
            const routeFilter = document.getElementById('route-filter');
            routeFilter.innerHTML = '<option value="">All Routes</option>';
            
            routes.forEach(route => {
                const option = document.createElement('option');
                option.value = route.id;
                option.textContent = `${route.from_location} → ${route.to_location}`;
                routeFilter.appendChild(option);
            });
        }

        // Request bookings update with filters
        function requestBookingsUpdate() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const filters = {
                    date: document.getElementById('date-filter').value || '',
                    route: document.getElementById('route-filter').value || '',
                    status: document.getElementById('status-filter').value || '',
                    page: currentPage,
                    itemsPerPage: itemsPerPage
                };
                
                // Show loading state
                const tbody = document.getElementById('bookings-table-body');
                tbody.innerHTML = '<tr><td colspan="7" class="px-6 py-4 text-center">Loading bookings...</td></tr>';
                
                console.log('Sending GET_BOOKINGS request with filters:', filters);
                
                ws.send(JSON.stringify({
                    type: 'GET_BOOKINGS',
                    filters: filters
                }));
            } else {
                console.log('WebSocket not connected. Attempting to reconnect...');
                connectWebSocket();
            }
        }

        function updateBookingsTable(bookings) {
            const tbody = document.getElementById('bookings-table-body');
            tbody.innerHTML = '';

            if (bookings.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" class="px-6 py-4 text-center">No bookings found</td></tr>';
                return;
            }

            bookings.forEach(booking => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${booking.id}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">${booking.customer_name}</div>
                        <div class="text-sm text-gray-500">${booking.email}</div>
                        <div class="text-sm text-gray-500">${booking.phone}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${booking.routes ? `${booking.routes.from_location} → ${booking.routes.to_location}` : 'N/A'}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">${new Date(booking.travel_date).toLocaleDateString()}</div>
                        <div class="text-sm text-gray-500">Created: ${new Date(booking.created_at).toLocaleDateString()}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        GH₵ ${parseFloat(booking.total_amount).toFixed(2)}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(booking.booking_status)}">
                            ${booking.booking_status}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button onclick="viewBooking('${booking.id}')" class="text-primary hover:text-secondary mr-3">
                            View
                        </button>
                        ${booking.booking_status === 'confirmed' ? `
                            <button onclick="resendTicket('${booking.id}')" class="text-green-600 hover:text-green-800">
                                Resend Ticket
                            </button>
                        ` : ''}
                    </td>
                `;
                tbody.appendChild(tr);
            });
        }

        function getStatusColor(status) {
            switch (status.toLowerCase()) {
                case 'confirmed':
                    return 'bg-yellow-100 text-yellow-800';
                case 'scanned':
                    return 'bg-green-100 text-green-800';
                case 'cancelled':
                    return 'bg-red-100 text-red-800';
                case 'pending':
                    return 'bg-gray-100 text-gray-800';
                default:
                    return 'bg-gray-100 text-gray-800';
            }
        }

        function showError(message) {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: message,
                confirmButtonColor: '#1a56db'
            });
        }

        function previousPage() {
            if (currentPage > 1) {
                currentPage--;
                requestBookingsUpdate();
            }
        }

        function nextPage() {
            currentPage++;
            requestBookingsUpdate();
        }

        function updatePagination(total, currentPage, itemsPerPage) {
            const start = ((currentPage - 1) * itemsPerPage) + 1;
            const end = Math.min(start + itemsPerPage - 1, total);
            
            document.getElementById('page-start').textContent = total > 0 ? start : 0;
            document.getElementById('page-end').textContent = end;
            document.getElementById('total-items').textContent = total;

            // Update button states
            const prevButton = document.querySelector('button[onclick="previousPage()"]');
            const nextButton = document.querySelector('button[onclick="nextPage()"]');
            
            if (prevButton) {
                prevButton.disabled = currentPage <= 1;
                prevButton.classList.toggle('opacity-50', currentPage <= 1);
            }
            
            if (nextButton) {
                nextButton.disabled = end >= total;
                nextButton.classList.toggle('opacity-50', end >= total);
            }
        }

        function applyFilters() {
            currentPage = 1;
            requestBookingsUpdate();
        }

        // Initialize everything when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            initializeMobileMenu();
            connectWebSocket();
            
            // Add filter event listeners
            document.getElementById('date-filter').addEventListener('change', () => {
                currentPage = 1;
                requestBookingsUpdate();
            });
            
            document.getElementById('route-filter').addEventListener('change', () => {
                currentPage = 1;
                requestBookingsUpdate();
            });
            
            document.getElementById('status-filter').addEventListener('change', () => {
                currentPage = 1;
                requestBookingsUpdate();
            });
        });

        function logout() {
            localStorage.removeItem('adminAuthenticated');
            window.location.href = 'login.html';
        }

        function resendTicket(bookingId) {
            if (!bookingId) {
                showError('Invalid booking ID');
                return;
            }

            Swal.fire({
                title: 'Resend Ticket',
                text: 'Are you sure you want to resend the ticket?',
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'Yes, resend it',
                cancelButtonText: 'Cancel',
                confirmButtonColor: '#1a56db',
                cancelButtonColor: '#6b7280'
            }).then((result) => {
                if (result.isConfirmed) {
                    Swal.fire({
                        title: 'Resending Ticket',
                        text: 'Please wait...',
                        allowOutsideClick: false,
                        showConfirmButton: false,
                        willOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    if (ws && ws.readyState === WebSocket.OPEN) {
                        console.log('Resending ticket for booking:', bookingId);
                        ws.send(JSON.stringify({
                            type: 'RESEND_TICKET',
                            data: {
                                booking_id: bookingId.trim()
                            }
                        }));
                    } else {
                        showError('Connection lost. Please refresh the page.');
                    }
                }
            });
        }

        function handleTicketResendResult(data) {
            if (data.success) {
                Swal.fire({
                    icon: 'success',
                    title: 'Success',
                    text: 'Ticket has been resent successfully',
                    confirmButtonColor: '#1a56db'
                });
            } else {
                showError(data.error || 'Failed to resend ticket');
            }
        }

        function viewBooking(bookingId) {
            Swal.fire({
                title: 'Viewing Booking',
                text: 'Loading booking details...',
                allowOutsideClick: false,
                showConfirmButton: false,
                willOpen: () => {
                    Swal.showLoading();
                }
            });

            // Find the booking in the current table
            const bookingRow = Array.from(document.getElementById('bookings-table-body').getElementsByTagName('tr'))
                .find(row => row.cells[0]?.textContent.trim() === bookingId);

            if (bookingRow) {
                const cells = bookingRow.cells;
                const bookingDetails = {
                    id: cells[0].textContent.trim(),
                    customer: cells[1].innerHTML,
                    route: cells[2].textContent.trim(),
                    date: cells[3].innerHTML,
                    amount: cells[4].textContent.trim(),
                    status: cells[5].textContent.trim()
                };

                Swal.fire({
                    title: `Booking #${bookingDetails.id}`,
                    html: `
                        <div class="text-left">
                            <p class="mb-2"><strong>Customer Information:</strong></p>
                            <div class="ml-4 mb-3">${bookingDetails.customer}</div>
                            
                            <p class="mb-2"><strong>Route:</strong></p>
                            <div class="ml-4 mb-3">${bookingDetails.route}</div>
                            
                            <p class="mb-2"><strong>Date Information:</strong></p>
                            <div class="ml-4 mb-3">${bookingDetails.date}</div>
                            
                            <p class="mb-2"><strong>Amount:</strong></p>
                            <div class="ml-4 mb-3">${bookingDetails.amount}</div>
                            
                            <p class="mb-2"><strong>Status:</strong></p>
                            <div class="ml-4">${bookingDetails.status}</div>
                        </div>
                    `,
                    confirmButtonText: 'Close',
                    confirmButtonColor: '#1a56db',
                    customClass: {
                        htmlContainer: 'text-left'
                    }
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Booking details not found',
                    confirmButtonColor: '#1a56db'
                });
            }
        }
    </script>
</body>
</html> 