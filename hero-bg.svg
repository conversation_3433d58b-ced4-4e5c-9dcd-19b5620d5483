<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#4F46E5;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#818CF8;stop-opacity:1" />
        </linearGradient>
        <pattern id="pattern-circles" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
            <circle cx="20" cy="20" r="1" fill="white" opacity="0.2"/>
        </pattern>
    </defs>
    
    <!-- Background -->
    <rect width="100%" height="100%" fill="url(#bg-gradient)"/>
    
    <!-- Pattern overlay -->
    <rect width="100%" height="100%" fill="url(#pattern-circles)"/>
    
    <!-- Abstract shapes -->
    <path d="M0,400 Q480,200 960,400 T1920,400" fill="none" stroke="white" stroke-width="2" opacity="0.1"/>
    <path d="M0,600 Q480,400 960,600 T1920,600" fill="none" stroke="white" stroke-width="2" opacity="0.1"/>
    <path d="M0,800 Q480,600 960,800 T1920,800" fill="none" stroke="white" stroke-width="2" opacity="0.1"/>
</svg> 