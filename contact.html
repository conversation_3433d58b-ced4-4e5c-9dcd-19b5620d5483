<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Contact Us - ABC Travels</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <link rel="icon" type="image/svg+xml" href="logo.svg" />
    <script>
      // Configuration for different environments
      const config = {
        development: {
          wsUrl: "ws://localhost:8080",
          apiUrl: "http://localhost:8080",
        },
        production: {
          wsUrl: "wss://deployed-abc-travels.onrender.com",
          apiUrl: "https://deployed-abc-travels.onrender.com",
        },
      };

      // Determine the current environment
      const environment =
        window.location.hostname === "abctravels.netlify.app" ||
        window.location.hostname.includes("netlify.app")
          ? "production"
          : "development";

      // Export the configuration
      const currentConfig = config[environment];
    </script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "#1a56db",
              secondary: "#1e429f",
              accent: "#e3f2fd",
            },
          },
        },
      };
    </script>
  </head>
  <body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg fixed w-full z-50">
      <div class="max-w-7xl mx-auto px-4">
        <div class="flex justify-between items-center h-16">
          <div class="flex items-center">
            <a href="/" class="flex items-center">
              <img src="logo.svg" alt="ABC Travels Logo" class="h-8 w-auto" />
            </a>
          </div>
          <div class="hidden md:flex items-center space-x-4">
            <a
              href="/"
              class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium"
              >Home</a
            >
            <a
              href="/routes.html"
              class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium"
              >Routes</a
            >
            <a
              href="/about.html"
              class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium"
              >About</a
            >
            <a
              href="/faq.html"
              class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium"
              >FAQ</a
            >
            <a
              href="/privacy.html"
              class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium"
              >Privacy Policy</a
            >
            <a
              href="/contact.html"
              class="text-primary px-3 py-2 rounded-md text-sm font-medium"
              >Contact</a
            >
          </div>
          <div class="md:hidden flex items-center">
            <button
              id="mobile-menu-button"
              class="text-gray-700 hover:text-primary"
            >
              <svg
                class="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>
      <!-- Mobile Menu -->
      <div id="mobile-menu" class="hidden md:hidden bg-white">
        <div class="px-2 pt-2 pb-3 space-y-1">
          <a
            href="/"
            class="block text-gray-700 hover:text-primary px-3 py-2 rounded-md text-base font-medium"
            >Home</a
          >
          <a
            href="/routes.html"
            class="block text-gray-700 hover:text-primary px-3 py-2 rounded-md text-base font-medium"
            >Routes</a
          >
          <a
            href="/about.html"
            class="block text-gray-700 hover:text-primary px-3 py-2 rounded-md text-base font-medium"
            >About</a
          >
          <a
            href="/faq.html"
            class="block text-gray-700 hover:text-primary px-3 py-2 rounded-md text-base font-medium"
            >FAQ</a
          >
          <a
            href="/privacy.html"
            class="block text-gray-700 hover:text-primary px-3 py-2 rounded-md text-base font-medium"
            >Privacy Policy</a
          >
          <a
            href="/contact.html"
            class="block text-primary px-3 py-2 rounded-md text-base font-medium"
            >Contact</a
          >
        </div>
      </div>
    </nav>

    <!-- Hero Section -->
    <section
      class="pt-20 bg-gradient-to-r from-primary to-secondary text-white"
    >
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
        <div class="text-center">
          <h1
            class="text-4xl tracking-tight font-extrabold sm:text-5xl md:text-6xl"
          >
            Contact Us
          </h1>
          <p
            class="mt-3 max-w-md mx-auto text-base text-gray-100 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl"
          >
            Get in touch with our team for support or inquiries
          </p>
        </div>
      </div>
      <div
        class="h-32 bg-white"
        style="clip-path: polygon(0 100%, 100% 0, 100% 100%, 0% 100%)"
      ></div>
    </section>

    <!-- Contact Content -->
    <section class="py-16 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
          <!-- Contact Form -->
          <div class="bg-white rounded-lg shadow-lg p-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">
              Send us a Message
            </h2>
            <form id="contact-form" class="space-y-6">
              <div>
                <label
                  for="name"
                  class="block text-sm font-medium text-gray-700"
                  >Full Name</label
                >
                <input
                  type="text"
                  id="name"
                  name="name"
                  required
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary"
                />
              </div>
              <div>
                <label
                  for="email"
                  class="block text-sm font-medium text-gray-700"
                  >Email Address</label
                >
                <input
                  type="email"
                  id="email"
                  name="email"
                  required
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary"
                />
              </div>
              <div>
                <label
                  for="subject"
                  class="block text-sm font-medium text-gray-700"
                  >Subject</label
                >
                <input
                  type="text"
                  id="subject"
                  name="subject"
                  required
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary"
                />
              </div>
              <div>
                <label
                  for="message"
                  class="block text-sm font-medium text-gray-700"
                  >Message</label
                >
                <textarea
                  id="message"
                  name="message"
                  rows="4"
                  required
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary"
                ></textarea>
              </div>
              <div>
                <button
                  type="submit"
                  class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                >
                  Send Message
                </button>
              </div>
            </form>
          </div>

          <!-- Contact Information -->
          <div class="space-y-8">
            <div>
              <h2 class="text-2xl font-bold text-gray-900 mb-6">
                Contact Information
              </h2>
              <div class="space-y-6">
                <div class="flex items-start">
                  <div class="flex-shrink-0">
                    <svg
                      class="h-6 w-6 text-primary"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                      />
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                    </svg>
                  </div>
                  <div class="ml-3">
                    <h3 class="text-lg font-medium text-gray-900">
                      Office Address
                    </h3>
                    <p class="mt-1 text-gray-600">
                      123 Main Street<br />
                      Takoradi, Ghana
                    </p>
                  </div>
                </div>
                <div class="flex items-start">
                  <div class="flex-shrink-0">
                    <svg
                      class="h-6 w-6 text-primary"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                      />
                    </svg>
                  </div>
                  <div class="ml-3">
                    <h3 class="text-lg font-medium text-gray-900">
                      Phone Numbers
                    </h3>
                    <p class="mt-1 text-gray-600">
                      Customer Support: +233 XX XXX XXXX<br />
                      Booking Inquiries: +233 XX XXX XXXX
                    </p>
                  </div>
                </div>
                <div class="flex items-start">
                  <div class="flex-shrink-0">
                    <svg
                      class="h-6 w-6 text-primary"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                      />
                    </svg>
                  </div>
                  <div class="ml-3">
                    <h3 class="text-lg font-medium text-gray-900">
                      Email Addresses
                    </h3>
                    <p class="mt-1 text-gray-600">
                      General Inquiries: <EMAIL><br />
                      Support: <EMAIL>
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Business Hours -->
            <div>
              <h2 class="text-2xl font-bold text-gray-900 mb-6">
                Business Hours
              </h2>
              <div class="space-y-4">
                <div class="flex justify-between">
                  <span class="text-gray-600">Monday - Friday</span>
                  <span class="text-gray-900 font-medium"
                    >8:00 AM - 8:00 PM</span
                  >
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Saturday</span>
                  <span class="text-gray-900 font-medium"
                    >9:00 AM - 6:00 PM</span
                  >
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Sunday</span>
                  <span class="text-gray-900 font-medium"
                    >10:00 AM - 4:00 PM</span
                  >
                </div>
              </div>
            </div>

            <!-- Map -->
            <div>
              <h2 class="text-2xl font-bold text-gray-900 mb-6">Location</h2>
              <div class="aspect-w-16 aspect-h-9">
                <iframe
                  src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d63602.38975829986!2d-1.7775106999999999!3d4.91466465!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0xfe7799a7b4d8bd9%3A0xd5df62ba6ac74520!2sTakoradi!5e0!3m2!1sen!2sgh!4v1739259004390!5m2!1sen!2sgh"
                  width="100%"
                  height="300"
                  style="border: 0"
                  allowfullscreen=""
                  loading="lazy"
                  class="rounded-lg shadow-lg"
                >
                </iframe>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800">
      <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
          <!-- Company Info -->
          <div class="col-span-1">
            <img
              src="logo.svg"
              alt="ABC Travels Logo"
              class="h-8 w-auto filter invert"
            />
            <p class="mt-4 text-gray-300">
              Your trusted partner for comfortable and reliable bus travel
              across Ghana.
            </p>
          </div>
          <!-- Quick Links -->
          <div class="col-span-1">
            <h3 class="text-white text-lg font-bold mb-4">Quick Links</h3>
            <ul class="space-y-2">
              <li>
                <a href="/routes.html" class="text-gray-300 hover:text-white"
                  >Routes</a
                >
              </li>
              <li>
                <a href="/about.html" class="text-gray-300 hover:text-white"
                  >About Us</a
                >
              </li>
              <li>
                <a href="/faq.html" class="text-gray-300 hover:text-white"
                  >FAQ</a
                >
              </li>
              <li>
                <a href="/contact.html" class="text-gray-300 hover:text-white"
                  >Contact</a
                >
              </li>
            </ul>
          </div>
          <!-- Contact Info -->
          <div class="col-span-1">
            <h3 class="text-white text-lg font-bold mb-4">Contact Us</h3>
            <ul class="space-y-2">
              <li class="text-gray-300">Phone: +233 XX XXX XXXX</li>
              <li class="text-gray-300">Email: <EMAIL></li>
              <li class="text-gray-300">Address: Takoradi, Ghana</li>
            </ul>
          </div>
          <!-- Social Media -->
          <div class="col-span-1">
            <h3 class="text-white text-lg font-bold mb-4">Follow Us</h3>
            <div class="flex space-x-4">
              <a href="#" class="text-gray-300 hover:text-white">
                <span class="sr-only">Facebook</span>
                <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                  <path
                    fill-rule="evenodd"
                    d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
                    clip-rule="evenodd"
                  />
                </svg>
              </a>
              <a href="#" class="text-gray-300 hover:text-white">
                <span class="sr-only">Twitter</span>
                <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                  <path
                    d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"
                  />
                </svg>
              </a>
              <a href="#" class="text-gray-300 hover:text-white">
                <span class="sr-only">Instagram</span>
                <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                  <path
                    fill-rule="evenodd"
                    d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"
                    clip-rule="evenodd"
                  />
                </svg>
              </a>
            </div>
          </div>
        </div>
        <div class="mt-8 border-t border-gray-700 pt-8">
          <p class="text-center text-gray-400">
            © 2025 ABC Travels. All rights reserved. | Developed by Griffin's
            Tech
          </p>
        </div>
      </div>
    </footer>

    <!-- Mobile Menu Toggle Script -->
    <script>
      let ws = null;
      let isConnecting = false;
      let reconnectAttempts = 0;
      const maxReconnectAttempts = 5;
      const reconnectDelay = 5000;

      function connectWebSocket() {
        if (ws !== null || isConnecting) return;

        if (reconnectAttempts >= maxReconnectAttempts) {
          showError(
            "Unable to connect to server. Please refresh the page to try again."
          );
          return;
        }

        isConnecting = true;
        console.log(`Connecting to WebSocket server at ${currentConfig.wsUrl}`);

        try {
          ws = new WebSocket(currentConfig.wsUrl);

          ws.onopen = () => {
            console.log("Connected to WebSocket server");
            isConnecting = false;
            reconnectAttempts = 0;
            updateConnectionStatus("Connected");
          };

          ws.onmessage = (event) => {
            try {
              const data = JSON.parse(event.data);
              console.log("Received WebSocket message:", data);

              if (data.type === "CONTACT_FORM_RESPONSE") {
                if (data.success) {
                  Swal.fire({
                    icon: "success",
                    title: "Message Sent!",
                    text: "Thank you for contacting us. We will get back to you soon.",
                    confirmButtonColor: "#1a56db",
                  });
                  document.getElementById("contact-form").reset();
                } else {
                  showError(
                    data.error || "Failed to send message. Please try again."
                  );
                }
              }
            } catch (error) {
              console.error("Error processing WebSocket message:", error);
              showError("Failed to process server response");
            }
          };

          ws.onclose = () => {
            console.log("Disconnected from WebSocket server");
            ws = null;
            isConnecting = false;
            reconnectAttempts++;
            updateConnectionStatus("Disconnected");

            if (reconnectAttempts < maxReconnectAttempts) {
              console.log(
                `Reconnecting... Attempt ${reconnectAttempts} of ${maxReconnectAttempts}`
              );
              setTimeout(connectWebSocket, reconnectDelay);
            }
          };

          ws.onerror = (error) => {
            console.error("WebSocket error:", error);
            ws = null;
            isConnecting = false;
            updateConnectionStatus("Error");
          };
        } catch (error) {
          console.error("Error creating WebSocket connection:", error);
          isConnecting = false;
          reconnectAttempts++;
          setTimeout(connectWebSocket, reconnectDelay);
        }
      }

      function updateConnectionStatus(status) {
        console.log(`WebSocket Status: ${status}`);
        // You can add UI elements to show connection status if needed
      }

      // Contact Form Submission
      document
        .getElementById("contact-form")
        .addEventListener("submit", function (e) {
          e.preventDefault();

          if (!ws || ws.readyState !== WebSocket.OPEN) {
            showError(
              "Connection to server lost. Please wait while we reconnect..."
            );
            connectWebSocket();
            return;
          }

          const formData = {
            name: document.getElementById("name").value,
            email: document.getElementById("email").value,
            subject: document.getElementById("subject").value,
            message: document.getElementById("message").value,
          };

          // Validate form data
          if (
            !formData.name.trim() ||
            !formData.email.trim() ||
            !formData.subject.trim() ||
            !formData.message.trim()
          ) {
            showError("Please fill in all fields");
            return;
          }

          // Basic email validation
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(formData.email)) {
            showError("Please enter a valid email address");
            return;
          }

          try {
            // Show loading state
            Swal.fire({
              title: "Sending...",
              text: "Please wait while we send your message",
              allowOutsideClick: false,
              showConfirmButton: false,
              willOpen: () => {
                Swal.showLoading();
              },
            });

            ws.send(
              JSON.stringify({
                type: "CONTACT_FORM",
                data: formData,
              })
            );
          } catch (error) {
            console.error("Error sending message:", error);
            showError("Failed to send message. Please try again.");
          }
        });

      function showError(message) {
        Swal.fire({
          icon: "error",
          title: "Error",
          text: message,
          confirmButtonColor: "#1a56db",
        });
      }

      // Initialize WebSocket connection when page loads
      window.addEventListener("load", () => {
        console.log("Initializing WebSocket connection...");
        connectWebSocket();
      });

      // Handle mobile menu toggle
      const mobileMenuButton = document.getElementById("mobile-menu-button");
      const mobileMenu = document.getElementById("mobile-menu");

      mobileMenuButton.addEventListener("click", () => {
        mobileMenu.classList.toggle("hidden");
      });
    </script>
  </body>
</html>
