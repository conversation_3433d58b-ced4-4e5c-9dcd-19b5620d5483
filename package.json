{"name": "abc-travels", "version": "1.0.0", "description": "ABC Travels Bus Ticketing System", "main": "server/websocket.js", "scripts": {"start": "node server/websocket.js", "dev": "nodemon server/websocket.js", "build": "echo 'No build step required for backend'"}, "dependencies": {"ws": "^8.16.0", "@supabase/supabase-js": "^2.39.3", "nodemailer": "^6.9.9", "dotenv": "^16.4.1", "express": "^4.18.2", "cors": "^2.8.5", "bcrypt": "^5.1.1"}, "devDependencies": {"nodemon": "^3.0.3"}, "engines": {"node": ">=18.0.0"}}