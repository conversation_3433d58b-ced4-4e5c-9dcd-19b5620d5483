-- ABC Travels Database Setup for Supabase
-- Run this SQL in your Supabase SQL Editor

-- Create triggers for updated_at timestamps (MUST BE CREATED FIRST)
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create routes table
CREATE TABLE routes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    departure_location VARCHAR(100) NOT NULL,
    destination VARCHAR(100) NOT NULL,
    departure_time TIME NOT NULL,
    arrival_time TIME NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    available_seats INTEGER DEFAULT 50,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create bookings table
CREATE TABLE bookings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    route_id UUID REFERENCES routes(id),
    customer_name VARCHAR(100) NOT NULL,
    customer_email VARCHAR(100) NOT NULL,
    customer_phone VARCHAR(20) NOT NULL,
    seats_booked INTEGER NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    booking_status VARCHAR(20) DEFAULT 'confirmed',
    payment_reference VARCHAR(100),
    qr_code_data TEXT,
    is_scanned BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create scans table
CREATE TABLE scans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    booking_id UUID REFERENCES bookings(id),
    scanned_by VARCHAR(100) NOT NULL,
    location VARCHAR(100) NOT NULL,
    scan_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    scan_status VARCHAR(20) NOT NULL, -- 'valid', 'invalid', 'duplicate'
    scan_message TEXT,
    device_info TEXT,
    is_first_scan BOOLEAN DEFAULT true
);

-- Create admins table
CREATE TABLE admins (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create locations table
CREATE TABLE locations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create triggers for updated_at timestamps
CREATE TRIGGER update_routes_updated_at
    BEFORE UPDATE ON routes
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bookings_updated_at
    BEFORE UPDATE ON bookings
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_locations_updated_at
    BEFORE UPDATE ON locations
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create indexes for better performance
CREATE INDEX idx_routes_departure_destination ON routes(departure_location, destination);
CREATE INDEX idx_routes_active ON routes(is_active);
CREATE INDEX idx_bookings_route_id ON bookings(route_id);
CREATE INDEX idx_bookings_email ON bookings(customer_email);
CREATE INDEX idx_bookings_status ON bookings(booking_status);
CREATE INDEX idx_scans_booking_id ON scans(booking_id);
CREATE INDEX idx_locations_name ON locations(name);

-- Insert default locations
INSERT INTO locations (name) VALUES
    ('Accra'),
    ('Kumasi'),
    ('Takoradi'),
    ('Tamale')
ON CONFLICT (name) DO NOTHING;

-- Insert sample routes
INSERT INTO routes (departure_location, destination, departure_time, arrival_time, price, available_seats) VALUES
    ('Accra', 'Kumasi', '06:00:00', '10:00:00', 50.00, 50),
    ('Accra', 'Kumasi', '14:00:00', '18:00:00', 50.00, 50),
    ('Kumasi', 'Accra', '07:00:00', '11:00:00', 50.00, 50),
    ('Kumasi', 'Accra', '15:00:00', '19:00:00', 50.00, 50),
    ('Accra', 'Takoradi', '08:00:00', '12:00:00', 40.00, 50),
    ('Takoradi', 'Accra', '09:00:00', '13:00:00', 40.00, 50),
    ('Accra', 'Tamale', '05:00:00', '13:00:00', 80.00, 50),
    ('Tamale', 'Accra', '06:00:00', '14:00:00', 80.00, 50)
ON CONFLICT DO NOTHING;

-- Note: The default admin account (username: admin, password: admin123) 
-- will be created automatically when the server starts up.
