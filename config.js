// Configuration for different environments
const config = {
    development: {
        wsUrl: 'ws://localhost:8080',
        apiUrl: 'http://localhost:8080',
        supabaseUrl: 'https://utqiwuvtwlmbagripkxx.supabase.co',
        supabaseKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0cWl3dXZ0d2xtYmFncmlwa3h4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg3OTgyNTUsImV4cCI6MjA1NDM3NDI1NX0.PMsf_SxXfRoCOoJmlksFAPhZuy6OQpyVF9WO08a-vnE'
    },
    production: {
        wsUrl: 'wss://deployed-abc-travels.onrender.com',
        apiUrl: 'https://deployed-abc-travels.onrender.com',
        supabaseUrl: 'https://utqiwuvtwlmbagripkxx.supabase.co',
        supabaseKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0cWl3dXZ0d2xtYmFncmlwa3h4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg3OTgyNTUsImV4cCI6MjA1NDM3NDI1NX0.PMsf_SxXfRoCOoJmlksFAPhZuy6OQpyVF9WO08a-vnE'
    }
};

// Determine the current environment
const environment = window.location.hostname === 'abctravels.netlify.app' || 
                   window.location.hostname.includes('netlify.app') ? 'production' : 'development';

// Export the configuration
const currentConfig = config[environment]; 