// Configuration for different environments
const config = {
  development: {
    wsUrl: "ws://localhost:8080",
    apiUrl: "http://localhost:8080",
    supabaseUrl: "https://rzjbejmdvjbhsfbdlaff.supabase.co",
    supabaseKey:
      "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJ6amJlam1kdmpiaHNmYmRsYWZmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NjIyNjkwOTksImV4cCI6MjA3Nzg0NTA5OX0.JnUnya76LFKD0vcI0fwUC9GTl9AlTx8YEEsyMmQPwjM",
  },
  production: {
    wsUrl: "wss://deployed-abc-travels.onrender.com",
    apiUrl: "https://deployed-abc-travels.onrender.com",
    supabaseUrl: "https://rzjbejmdvjbhsfbdlaff.supabase.co",
    supabaseKey:
      "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJ6amJlam1kdmpiaHNmYmRsYWZmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NjIyNjkwOTksImV4cCI6MjA3Nzg0NTA5OX0.JnUnya76LFKD0vcI0fwUC9GTl9AlTx8YEEsyMmQPwjM",
  },
};

// Determine the current environment
const environment =
  window.location.hostname === "abctravels.netlify.app" ||
  window.location.hostname.includes("netlify.app")
    ? "production"
    : "development";

// Export the configuration
const currentConfig = config[environment];
