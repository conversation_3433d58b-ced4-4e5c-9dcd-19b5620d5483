<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Routes - ABC Travels Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <link rel="icon" type="image/svg+xml" href="../logo.svg">
    <script src="../config.js"></script>
    <script>
        // Initialize Supabase client
        const { createClient } = supabase;
        const supabaseClient = createClient(
            currentConfig.supabaseUrl,
            currentConfig.supabaseKey
        );

        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1a56db',
                        secondary: '#1e429f',
                        accent: '#e3f2fd'
                    }
                }
            }
        }
    </script>
    <style>
        .sidebar-transition {
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .menu-icon {
            transition: transform 0.3s ease;
        }
        
        .menu-icon.active {
            transform: rotate(90deg);
        }
        
        .overlay {
            background-color: rgba(0, 0, 0, 0.5);
            transition: opacity 0.3s ease;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Check Authentication -->
    <script>
        if (localStorage.getItem('adminAuthenticated') !== 'true') {
            window.location.href = 'login.html';
        }
    </script>

    <!-- Mobile Menu Button -->
    <div class="fixed top-0 left-0 z-50 p-4 md:hidden">
        <button id="mobile-menu-button" class="text-gray-500 hover:text-gray-600 focus:outline-none">
            <svg class="h-6 w-6 menu-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
            </svg>
        </button>
    </div>

    <!-- Overlay -->
    <div id="overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden md:hidden"></div>

    <!-- Admin Layout -->
    <div class="min-h-screen flex">
        <!-- Sidebar -->
        <div id="sidebar" class="fixed inset-y-0 left-0 transform -translate-x-full md:relative md:translate-x-0 transition-transform duration-300 ease-in-out bg-gray-800 text-white w-64 py-6 flex flex-col z-50">
            <div class="px-6 mb-8">
                <div class="flex items-center">
                    <img src="../logo.svg" alt="ABC Travels Logo" class="h-8 w-auto filter invert">
                </div>
            </div>
            <nav class="flex-1">
                <a href="dashboard.html" class="flex items-center px-6 py-3 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200">
                    <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
                    </svg>
                    Dashboard
                </a>
                <a href="routes.html" class="flex items-center px-6 py-3 bg-gray-900 text-white">
                    <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"/>
                    </svg>
                    Routes
                </a>
                <a href="bookings.html" class="flex items-center px-6 py-3 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200">
                    <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 4v12l-4-2-4 2V4M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                    </svg>
                    Bookings
                </a>
                <a href="scanner.html" class="flex items-center px-6 py-3 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200">
                    <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"/>
                    </svg>
                    QR Scanner
                </a>
                <!-- <a href="reports.html" class="flex items-center px-6 py-3 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200">
                    <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                    </svg>
                    Reports
                </a> -->
            </nav>
            <div class="px-6 pt-4 pb-2 border-t border-gray-700">
                <button onclick="logout()" class="flex items-center px-4 py-2 text-gray-300 hover:text-white transition-colors duration-200">
                    <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
                    </svg>
                    Logout
                </button>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1">
            <header class="bg-white shadow-sm">
                <div class="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8 flex justify-between items-center">
                    <h1 class="text-lg font-semibold text-gray-900">Routes</h1>
                    <button onclick="showRouteDialog()" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2">
                        Add New Route
                    </button>
                </div>
            </header>

            <main class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                <!-- Routes Table -->
                <div class="bg-white rounded-lg shadow overflow-hidden">
                    <div class="min-w-full divide-y divide-gray-200">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Location
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Allowed Luggage (kg)
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Price
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Seats Available
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Status
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Departure Time
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions
                                    </th>
                                    <!-- <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Resend Ticket
                                    </th> -->
                                    <!-- <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions
                                    </th> -->
                                </tr>
                            </thead>
                            <tbody id="routes-body" class="bg-white divide-y divide-gray-200">
                                <!-- Routes will be inserted here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        async function loadRoutes() {
            try {
                const { data: routes, error } = await supabaseClient
                    .from('routes')
                    .select(`
                        id,
                        from_location,
                        to_location,
                        departure_time,
                        distance_km,
                        price,
                        available_seats,
                        description,
                        is_active,
                        created_at,
                        updated_at,
                        bookings:bookings(count)
                    `)
                    .order('created_at', { ascending: false });

                if (error) throw error;

                const routesBody = document.getElementById('routes-body');
                routesBody.innerHTML = '';

                routes.forEach(route => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">${route.from_location} → ${route.to_location}</div>
                            <div class="text-xs text-gray-500">Departure: ${route.departure_time}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">${route.distance_km} kg</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">GH₵ ${route.price.toFixed(2)}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">${route.available_seats}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                ${route.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                                ${route.is_active ? 'Active' : 'Inactive'}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            ${new Date(route.created_at).toLocaleString()}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <button onclick="editRoute('${route.id}')" class="text-primary hover:text-secondary">
                                Edit
                            </button>
                            <button onclick="deleteRoute('${route.id}')" class="ml-3 text-red-600 hover:text-red-800">
                                Delete
                            </button>
                        </td>
                    `;
                    routesBody.appendChild(row);
                });
            } catch (error) {
                console.error('Error loading routes:', error);
                showError('Failed to load routes');
            }
        }

        async function showRouteDialog(route = null) {
            const isEdit = !!route;
            
            // Fetch existing locations
            const { data: locations, error: locationsError } = await supabaseClient
                .from('locations')
                .select('*')
                .order('name');

            if (locationsError) {
                console.error('Error fetching locations:', locationsError);
                showError('Failed to load locations');
                return;
            }

            const { value: formValues } = await Swal.fire({
                title: isEdit ? 'Edit Route' : 'Add New Route',
                html: `
                    <form id="route-form" class="text-left">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="col-span-1">
                                <label class="block text-sm font-medium text-gray-700 mb-1">From</label>
                                <div class="flex space-x-2">
                                    <select id="from_location" class="flex-1 rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary" required>
                                        <option value="">Select departure city</option>
                                        ${locations.map(loc => `
                                            <option value="${loc.name}" ${route?.from_location === loc.name ? 'selected' : ''}>
                                                ${loc.name}
                                            </option>
                                        `).join('')}
                                    </select>
                                    <button type="button" onclick="addNewLocation('from')" 
                                        class="px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                                        <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            <div class="col-span-1">
                                <label class="block text-sm font-medium text-gray-700 mb-1">To</label>
                                <div class="flex space-x-2">
                                    <select id="to_location" class="flex-1 rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary" required>
                                        <option value="">Select destination city</option>
                                        ${locations.map(loc => `
                                            <option value="${loc.name}" ${route?.to_location === loc.name ? 'selected' : ''}>
                                                ${loc.name}
                                            </option>
                                        `).join('')}
                                    </select>
                                    <button type="button" onclick="addNewLocation('to')" 
                                        class="px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                                        <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            <div class="col-span-1">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Departure Time</label>
                                <input type="time" id="departure_time" class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary" 
                                    value="${route?.departure_time || ''}" required>
                            </div>
                            <div class="col-span-1">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Max Luggage Allowed (kg)</label>
                                <input type="number" id="distance_km" min="1" step="0.1" class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary"
                                    value="${route?.distance_km || ''}" required>
                            </div>
                            <div class="col-span-1">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Price (GH₵)</label>
                                <input type="number" id="price" min="0" step="0.01" class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary" 
                                    value="${route?.price || ''}" required>
                            </div>
                            <div class="col-span-1">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Available Seats</label>
                                <input type="number" id="available_seats" min="0" class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary" 
                                    value="${route?.available_seats || ''}" required>
                            </div>
                            <div class="col-span-2">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                                <textarea id="description" rows="3" class="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary">${route?.description || ''}</textarea>
                            </div>
                            <div class="col-span-2">
                                <label class="flex items-center space-x-2">
                                    <input type="checkbox" id="is_active" class="rounded border-gray-300 text-primary focus:ring-primary" 
                                        ${route?.is_active !== false ? 'checked' : ''}>
                                    <span class="text-sm font-medium text-gray-700">Active Route</span>
                                </label>
                            </div>
                        </div>
                    </form>
                `,
                showCancelButton: true,
                confirmButtonText: isEdit ? 'Update Route' : 'Add Route',
                confirmButtonColor: '#1a56db',
                cancelButtonColor: '#374151',
                focusConfirm: false,
                preConfirm: () => {
                    const form = document.getElementById('route-form');
                    if (!form.checkValidity()) {
                        form.reportValidity();
                        return false;
                    }

                    const fromLocation = document.getElementById('from_location').value;
                    const toLocation = document.getElementById('to_location').value;

                    if (fromLocation === toLocation) {
                        Swal.showValidationMessage('Departure and destination cities cannot be the same');
                        return false;
                    }

                    return {
                        from_location: fromLocation,
                        to_location: toLocation,
                        departure_time: document.getElementById('departure_time').value,
                        distance_km: parseFloat(document.getElementById('distance_km').value),
                        price: parseFloat(document.getElementById('price').value),
                        available_seats: parseInt(document.getElementById('available_seats').value),
                        description: document.getElementById('description').value.trim(),
                        is_active: document.getElementById('is_active').checked
                    };
                }
            });

            if (formValues) {
                try {
                    if (isEdit) {
                        const { error } = await supabaseClient
                            .from('routes')
                            .update(formValues)
                            .eq('id', route.id);

                        if (error) throw error;

                        showSuccess('Route updated successfully');
                    } else {
                        const { error } = await supabaseClient
                            .from('routes')
                            .insert([formValues]);

                        if (error) throw error;

                        showSuccess('Route added successfully');
                    }

                    loadRoutes();
                } catch (error) {
                    console.error('Error saving route:', error);
                    showError('Failed to save route');
                }
            }
        }

        async function editRoute(routeId) {
            try {
                const { data: route, error } = await supabaseClient
                    .from('routes')
                    .select('*')
                    .eq('id', routeId)
                    .single();

                if (error) throw error;

                showRouteDialog(route);
            } catch (error) {
                console.error('Error editing route:', error);
                showError('Failed to load route details');
            }
        }

        async function deleteRoute(routeId) {
            const result = await Swal.fire({
                title: 'Are you sure?',
                text: 'This action cannot be undone',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc2626',
                cancelButtonColor: '#374151',
                confirmButtonText: 'Yes, delete it'
            });

            if (result.isConfirmed) {
                try {
                    const { error } = await supabaseClient
                        .from('routes')
                        .delete()
                        .eq('id', routeId);

                    if (error) throw error;

                    showSuccess('Route deleted successfully');
                    loadRoutes();
                } catch (error) {
                    console.error('Error deleting route:', error);
                    showError('Failed to delete route');
                }
            }
        }

        function showSuccess(message) {
            Swal.fire({
                icon: 'success',
                title: 'Success',
                text: message,
                confirmButtonColor: '#1a56db'
            });
        }

        function showError(message) {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: message,
                confirmButtonColor: '#1a56db'
            });
        }

        async function addNewLocation(type) {
            const { value: locationName } = await Swal.fire({
                title: 'Add New Location',
                input: 'text',
                inputLabel: 'Location Name',
                inputPlaceholder: 'Enter the location name',
                showCancelButton: true,
                inputValidator: (value) => {
                    if (!value) {
                        return 'Location name is required';
                    }
                }
            });

            if (locationName) {
                try {
                    // Check if location already exists
                    const { data: existingLocation, error: checkError } = await supabaseClient
                        .from('locations')
                        .select('name')
                        .eq('name', locationName)
                        .single();

                    if (checkError && checkError.code !== 'PGRST116') {
                        throw checkError;
                    }

                    if (existingLocation) {
                        showError('This location already exists');
                        return;
                    }

                    // Add new location
                    const { error: insertError } = await supabaseClient
                        .from('locations')
                        .insert([{ name: locationName }]);

                    if (insertError) throw insertError;

                    // Refresh the route dialog to show the new location
                    const currentRoute = document.getElementById('route-form') ? {
                        from_location: document.getElementById('from_location').value,
                        to_location: document.getElementById('to_location').value,
                        departure_time: document.getElementById('departure_time').value,
                        distance_km: document.getElementById('distance_km').value,
                        price: document.getElementById('price').value,
                        available_seats: document.getElementById('available_seats').value,
                        description: document.getElementById('description').value,
                        is_active: document.getElementById('is_active').checked
                    } : null;

                    showRouteDialog(currentRoute);

                    // Select the new location in the appropriate dropdown
                    setTimeout(() => {
                        const dropdown = document.getElementById(type + '_location');
                        if (dropdown) {
                            dropdown.value = locationName;
                        }
                    }, 100);

                    showSuccess('Location added successfully');
                } catch (error) {
                    console.error('Error adding location:', error);
                    showError('Failed to add location');
                }
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            loadRoutes();
        });

        // Mobile Menu
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('overlay');
        const menuIcon = mobileMenuButton.querySelector('.menu-icon');

        mobileMenuButton.addEventListener('click', () => {
            sidebar.classList.toggle('-translate-x-full');
            overlay.classList.toggle('hidden');
            menuIcon.classList.toggle('active');
        });

        overlay.addEventListener('click', () => {
            sidebar.classList.add('-translate-x-full');
            overlay.classList.add('hidden');
            menuIcon.classList.remove('active');
        });

        function logout() {
            localStorage.removeItem('adminAuthenticated');
            window.location.href = 'login.html';
        }
    </script>
</body>
</html> 