case 'NEW_BOOKING':
    try {
        const bookingData = message.data;
        
        // Update available seats for the route
        const route = mockData.routes.find(r => r.id === bookingData.route.id);
        if (route) {
            route.availableSeats -= bookingData.passengers;
            
            // Notify all clients about the updated route
            broadcast(JSON.stringify({
                type: 'ROUTE_UPDATE',
                data: route
            }));
        }

        // Send email with ticket
        const emailResult = await sendTicketEmail({
            to: bookingData.email,
            name: bookingData.customerName,
            ticketId: bookingData.bookingId,
            route: bookingData.route,
            passengers: bookingData.passengers,
            ticketImage: bookingData.ticketImage
        });

        // Send email status back to the client
        ws.send(JSON.stringify({
            type: 'EMAIL_STATUS',
            bookingId: bookingData.bookingId,
            success: emailResult.success,
            error: emailResult.error
        }));

    } catch (error) {
        console.error('Error processing booking:', error);
        ws.send(JSON.stringify({
            type: 'EMAIL_STATUS',
            bookingId: message.data.bookingId,
            success: false,
            error: error.message
        }));
    }
    break; 