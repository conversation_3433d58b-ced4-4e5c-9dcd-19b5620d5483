<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ABC Travels - Your Trusted Bus Ticketing Partner</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://js.paystack.co/v1/inline.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/qrcodejs/1.0.0/qrcode.min.js"></script>
    <link rel="icon" type="image/svg+xml" href="logo.svg">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1a56db',
                        secondary: '#1e429f',
                        accent: '#e3f2fd'
                    }
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .text-shadow {
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
            }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg fixed w-full z-50">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <a href="/" class="flex items-center">
                        <img src="logo.svg" alt="ABC Travels Logo" class="h-8 w-auto">
                    </a>
                </div>
                <div class="hidden md:flex items-center space-x-4">
                    <a href="/" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">Home</a>
                    <a href="/routes.html" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">Routes</a>
                    <a href="/about.html" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">About</a>
                    <a href="/faq.html" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">FAQ</a>
                    <a href="/privacy.html" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">Privacy Policy</a>
                    <a href="/contact.html" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium">Contact</a>
                </div>
                <div class="md:hidden flex items-center">
                    <button id="mobile-menu-button" class="text-gray-700 hover:text-primary">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        <!-- Mobile Menu -->
        <div id="mobile-menu" class="hidden md:hidden bg-white">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="/" class="block text-gray-700 hover:text-primary px-3 py-2 rounded-md text-base font-medium">Home</a>
                <a href="/routes.html" class="block text-gray-700 hover:text-primary px-3 py-2 rounded-md text-base font-medium">Routes</a>
                <a href="/about.html" class="block text-gray-700 hover:text-primary px-3 py-2 rounded-md text-base font-medium">About</a>
                <a href="/faq.html" class="block text-gray-700 hover:text-primary px-3 py-2 rounded-md text-base font-medium">FAQ</a>
                <a href="/privacy.html" class="block text-gray-700 hover:text-primary px-3 py-2 rounded-md text-base font-medium">Privacy Policy</a>
                <a href="/contact.html" class="block text-gray-700 hover:text-primary px-3 py-2 rounded-md text-base font-medium">Contact</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="pt-20 bg-gradient-to-b from-primary to-secondary text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
            <div class="text-center">
                <h1 class="text-4xl tracking-tight font-extrabold sm:text-5xl md:text-6xl">
                    <span class="block">Travel Ghana with Comfort</span>
                    <span class="block text-accent mt-2">Book Your Bus Tickets Online</span>
                </h1>
                <p class="mt-3 max-w-md mx-auto text-base text-gray-100 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl">
                    Experience seamless bus travel across Ghana. Book tickets instantly for routes connecting Takoradi, Accra, Kumasi, Tamale, and beyond.
                </p>
                <div class="mt-5 max-w-md mx-auto sm:flex sm:justify-center md:mt-8">
                    <div class="rounded-md shadow">
                        <a href="/routes.html" class="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-primary bg-white hover:bg-gray-50 md:py-4 md:text-lg md:px-10">
                            Book Now
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="h-32 bg-white" style="clip-path: polygon(0 100%, 100% 0, 100% 100%, 0% 100%);"></div>
    </section>

    <!-- Features Section -->
    <section class="py-12 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="lg:text-center">
                <h2 class="text-base text-primary font-semibold tracking-wide uppercase">Why Choose Us</h2>
                <p class="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
                    Travel Smart, Travel Safe
                </p>
            </div>
            <div class="mt-10">
                <div class="grid grid-cols-1 gap-10 sm:grid-cols-2 lg:grid-cols-3">
                    <!-- Feature 1 -->
                    <div class="flex flex-col items-center">
                        <div class="flex items-center justify-center h-12 w-12 rounded-md bg-primary text-white">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <h3 class="mt-4 text-lg font-medium text-gray-900">Quick Booking</h3>
                        <p class="mt-2 text-base text-gray-500 text-center">
                            Book your tickets in minutes with our easy-to-use platform
                        </p>
                    </div>
                    <!-- Feature 2 -->
                    <div class="flex flex-col items-center">
                        <div class="flex items-center justify-center h-12 w-12 rounded-md bg-primary text-white">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                            </svg>
                        </div>
                        <h3 class="mt-4 text-lg font-medium text-gray-900">Secure Payments</h3>
                        <p class="mt-2 text-base text-gray-500 text-center">
                            Safe and secure payment processing with Paystack
                        </p>
                    </div>
                    <!-- Feature 3 -->
                    <div class="flex flex-col items-center">
                        <div class="flex items-center justify-center h-12 w-12 rounded-md bg-primary text-white">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3"/>
                            </svg>
                        </div>
                        <h3 class="mt-4 text-lg font-medium text-gray-900">Digital Tickets</h3>
                        <p class="mt-2 text-base text-gray-500 text-center">
                            QR code tickets sent directly to your email
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Popular Routes Section -->
    <section class="py-12 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="lg:text-center">
                <h2 class="text-base text-primary font-semibold tracking-wide uppercase">Popular Routes</h2>
                <p class="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
                    Connecting Ghana
                </p>
            </div>
            <div class="mt-10">
                <div class="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
                    <!-- Route cards will be dynamically populated here -->
                </div>
                <div class="text-center mt-8">
                    <a href="/routes.html" class="inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary hover:bg-secondary">
                        View All Routes
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section class="py-12 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="lg:text-center">
                <h2 class="text-base text-primary font-semibold tracking-wide uppercase">Process</h2>
                <p class="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
                    How It Works
                </p>
            </div>
            <div class="mt-10">
                <div class="grid grid-cols-1 gap-10 sm:grid-cols-2 lg:grid-cols-4">
                    <!-- Step 1 -->
                    <div class="relative">
                        <div class="flex flex-col items-center">
                            <div class="flex items-center justify-center h-12 w-12 rounded-full bg-primary text-white">
                                1
                            </div>
                            <h3 class="mt-4 text-lg font-medium text-gray-900">Select Route</h3>
                            <p class="mt-2 text-base text-gray-500 text-center">
                                Choose your departure and destination cities
                            </p>
                        </div>
                    </div>
                    <!-- Step 2 -->
                    <div class="relative">
                        <div class="flex flex-col items-center">
                            <div class="flex items-center justify-center h-12 w-12 rounded-full bg-primary text-white">
                                2
                            </div>
                            <h3 class="mt-4 text-lg font-medium text-gray-900">Fill Details</h3>
                            <p class="mt-2 text-base text-gray-500 text-center">
                                Enter passenger information
                            </p>
                        </div>
                    </div>
                    <!-- Step 3 -->
                    <div class="relative">
                        <div class="flex flex-col items-center">
                            <div class="flex items-center justify-center h-12 w-12 rounded-full bg-primary text-white">
                                3
                            </div>
                            <h3 class="mt-4 text-lg font-medium text-gray-900">Make Payment</h3>
                            <p class="mt-2 text-base text-gray-500 text-center">
                                Pay securely with Paystack
                            </p>
                        </div>
                    </div>
                    <!-- Step 4 -->
                    <div class="relative">
                        <div class="flex flex-col items-center">
                            <div class="flex items-center justify-center h-12 w-12 rounded-full bg-primary text-white">
                                4
                            </div>
                            <h3 class="mt-4 text-lg font-medium text-gray-900">Get Ticket</h3>
                            <p class="mt-2 text-base text-gray-500 text-center">
                                Receive QR ticket via email
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="py-12 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="lg:text-center">
                <h2 class="text-base text-primary font-semibold tracking-wide uppercase">Testimonials</h2>
                <p class="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
                    What Our Customers Say
                </p>
            </div>
            <div class="mt-10">
                <div class="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
                    <!-- Testimonial 1 -->
                    <div class="bg-white shadow-lg rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-10 w-10">
                                <img class="h-10 w-10 rounded-full" src="https://ui-avatars.com/api/?name=John+Doe" alt="">
                            </div>
                            <div class="ml-4">
                                <h4 class="text-lg font-bold">John Adofo</h4>
                                <p class="text-gray-500">Regular Traveler</p>
                            </div>
                        </div>
                        <p class="mt-4 text-gray-600">
                            "The booking process was so simple and straightforward. I received my ticket instantly!"
                        </p>
                    </div>
                    <!-- Testimonial 2 -->
                    <div class="bg-white shadow-lg rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-10 w-10">
                                <img class="h-10 w-10 rounded-full" src="https://ui-avatars.com/api/?name=Sarah+Smith" alt="">
                            </div>
                            <div class="ml-4">
                                <h4 class="text-lg font-bold">Sarah Smith</h4>
                                <p class="text-gray-500">Business Traveler</p>
                            </div>
                        </div>
                        <p class="mt-4 text-gray-600">
                            "The QR code ticket system is very convenient. No more paper tickets to worry about!"
                        </p>
                    </div>
                    <!-- Testimonial 3 -->
                    <div class="bg-white shadow-lg rounded-lg p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-10 w-10">
                                <img class="h-10 w-10 rounded-full" src="https://ui-avatars.com/api/?name=Kwame+Mensah" alt="">
                            </div>
                            <div class="ml-4">
                                <h4 class="text-lg font-bold">Kwame Mensah</h4>
                                <p class="text-gray-500">Student</p>
                            </div>
                        </div>
                        <p class="mt-4 text-gray-600">
                            "Great service and very reliable. I use ABC Travels for all my journeys now!"
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Newsletter Section -->
    <section class="py-12 bg-primary">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="lg:text-center">
                <h2 class="text-3xl font-extrabold text-white sm:text-4xl">
                    Stay Updated
                </h2>
                <p class="mt-4 text-lg text-gray-200">
                    Subscribe to our newsletter for the latest updates and special offers
                </p>
                <div class="mt-8 flex justify-center">
                    <div class="inline-flex rounded-md shadow">
                        <form class="sm:flex">
                            <input type="email" required class="w-full px-5 py-3 placeholder-gray-500 focus:ring-2 focus:ring-offset-2 focus:ring-offset-primary focus:ring-white focus:outline-none rounded-l-md" placeholder="Enter your email">
                            <button type="submit" class="w-full sm:w-auto flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-r-md text-primary bg-white hover:bg-gray-50">
                                Subscribe
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800">
        <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Company Info -->
                <div class="col-span-1">
                    <img src="logo.svg" alt="ABC Travels Logo" class="h-8 w-auto filter invert">
                    <p class="mt-4 text-gray-300">
                        Your trusted partner for comfortable and reliable bus travel across Ghana.
                    </p>
                </div>
                <!-- Quick Links -->
                <div class="col-span-1">
                    <h3 class="text-white text-lg font-bold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="/routes.html" class="text-gray-300 hover:text-white">Routes</a></li>
                        <li><a href="/about.html" class="text-gray-300 hover:text-white">About Us</a></li>
                        <li><a href="/faq.html" class="text-gray-300 hover:text-white">FAQ</a></li>
                        <li><a href="/contact.html" class="text-gray-300 hover:text-white">Contact</a></li>
                    </ul>
                </div>
                <!-- Contact Info -->
                <div class="col-span-1">
                    <h3 class="text-white text-lg font-bold mb-4">Contact Us</h3>
                    <ul class="space-y-2">
                        <li class="text-gray-300">Phone: +233 XX XXX XXXX</li>
                        <li class="text-gray-300">Email: <EMAIL></li>
                        <li class="text-gray-300">Address: Accra, Ghana</li>
                    </ul>
                </div>
                <!-- Social Media -->
                <div class="col-span-1">
                    <h3 class="text-white text-lg font-bold mb-4">Follow Us</h3>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-300 hover:text-white">
                            <span class="sr-only">Facebook</span>
                            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                <path fill-rule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clip-rule="evenodd"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-300 hover:text-white">
                            <span class="sr-only">Twitter</span>
                            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-300 hover:text-white">
                            <span class="sr-only">Instagram</span>
                            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                <path fill-rule="evenodd" d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" clip-rule="evenodd"/>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
            <div class="mt-8 border-t border-gray-700 pt-8">
                <p class="text-center text-gray-400">
                    © 2025 ABC Travels. All rights reserved. | Developed by Procurement and Supply Group 13
                </p>
            </div>
        </div>
    </footer>

    <!-- Mobile Menu Toggle Script -->
    <script>
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const mobileMenu = document.getElementById('mobile-menu');

        mobileMenuButton.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });
    </script>
</body>
</html> 