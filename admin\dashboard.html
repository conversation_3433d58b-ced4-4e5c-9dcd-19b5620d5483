<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - ABC Travels</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <link rel="icon" type="image/svg+xml" href="../logo.svg">
    <script src="../config.js"></script>
    <script>
        // Initialize Supabase client
        const { createClient } = supabase;
        const supabaseClient = createClient(
            currentConfig.supabaseUrl,
            currentConfig.supabaseKey
        );

        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1a56db',
                        secondary: '#1e429f',
                        accent: '#e3f2fd'
                    }
                }
            }
        }
    </script>
    <style>
        .sidebar-transition {
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .menu-icon {
            transition: transform 0.3s ease;
        }
        
        .menu-icon.active {
            transform: rotate(90deg);
        }
        
        .overlay {
            background-color: rgba(0, 0, 0, 0.5);
            transition: opacity 0.3s ease;
        }
        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Check Authentication -->
    <script>
        if (!localStorage.getItem('adminAuthenticated')) {
            window.location.href = 'login.html';
        }
    </script>

    <!-- Mobile Menu Button -->
    <div class="fixed top-0 left-0 z-50 p-4 md:hidden">
        <button id="mobile-menu-button" class="text-gray-500 hover:text-gray-600 focus:outline-none">
            <svg class="h-6 w-6 menu-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
            </svg>
        </button>
    </div>

    <!-- Overlay -->
    <div id="overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden md:hidden"></div>

    <!-- Admin Layout -->
    <div class="min-h-screen flex">
        <!-- Sidebar (hidden on mobile by default) -->
        <div id="sidebar" class="fixed inset-y-0 left-0 transform -translate-x-full md:relative md:translate-x-0 transition-transform duration-300 ease-in-out bg-gray-800 text-white w-64 py-6 flex flex-col z-50">
            <div class="px-6 mb-8">
                <div class="flex items-center">
                    <img src="../logo.svg" alt="ABC Travels Logo" class="h-8 w-auto filter invert">
                </div>
            </div>
            <nav class="flex-1">
                <a href="dashboard.html" class="flex items-center px-6 py-3 bg-gray-900 text-white">
                    <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
                    </svg>
                    Dashboard
                </a>
                <a href="routes.html" class="flex items-center px-6 py-3 text-gray-300 hover:bg-gray-700 hover:text-white">
                    <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"/>
                    </svg>
                    Routes
                </a>
                <a href="bookings.html" class="flex items-center px-6 py-3 text-gray-300 hover:bg-gray-700 hover:text-white">
                    <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 4v12l-4-2-4 2V4M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                    </svg>
                    Bookings
                </a>
                <a href="scanner.html" class="flex items-center px-6 py-3 text-gray-300 hover:bg-gray-700 hover:text-white">
                    <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"/>
                    </svg>
                    QR Scanner
                </a>
                <!-- <a href="reports.html" class="flex items-center px-6 py-3 text-gray-300 hover:bg-gray-700 hover:text-white transition-colors duration-200">
                    <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                    </svg>
                    Reports
                </a> -->
            </nav>
            <div class="px-6 pt-4 pb-2 border-t border-gray-700">
                <button onclick="logout()" class="flex items-center px-4 py-2 text-gray-300 hover:text-white">
                    <svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
                    </svg>
                    Logout
                </button>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 overflow-x-hidden">
            <!-- Top Bar -->
            <header class="bg-white shadow-sm sticky top-0 z-30">
                <div class="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8 flex justify-between items-center">
                    <h1 class="text-lg font-semibold text-gray-900">Dashboard</h1>
                    <div class="flex items-center space-x-4">
                        <div class="relative" id="notifications-dropdown">
                            <button class="text-gray-500 hover:text-gray-600">
                                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"/>
                                </svg>
                                <span class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500"></span>
                            </button>
                        </div>
                        <div class="relative" id="user-dropdown">
                            <button class="flex items-center space-x-2 text-gray-700 hover:text-gray-900">
                                <img src="https://ui-avatars.com/api/?name=Admin&background=1a56db&color=fff" alt="Admin" class="h-8 w-8 rounded-full">
                            </button>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Dashboard Content -->
            <main class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 4v12l-4-2-4 2V4M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-gray-500 text-sm font-medium">Total Bookings</h3>
                                <p class="text-3xl font-bold text-gray-900" id="total-bookings">0</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-green-100 text-green-600">
                                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-gray-500 text-sm font-medium">Today's Revenue</h3>
                                <p class="text-3xl font-bold text-gray-900" id="today-revenue">GH₵ 0.00</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"/>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-gray-500 text-sm font-medium">Active Routes</h3>
                                <p class="text-3xl font-bold text-gray-900" id="total-routes">0</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-lg shadow p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"/>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-gray-500 text-sm font-medium">Tickets Scanned</h3>
                                <p class="text-3xl font-bold text-gray-900" id="total-scans">0</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts Section -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                    <!-- Revenue Chart -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <h2 class="text-lg font-medium text-gray-900 mb-4">Revenue Overview</h2>
                        <div class="chart-container">
                            <canvas id="revenue-chart"></canvas>
                        </div>
                    </div>

                    <!-- Booking Trends -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <h2 class="text-lg font-medium text-gray-900 mb-4">Booking Trends</h2>
                        <div class="chart-container">
                            <canvas id="booking-chart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Revenue Analytics Section -->
                <div class="bg-white rounded-lg shadow p-6 mb-6">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-lg font-medium text-gray-900">Revenue Analytics</h2>
                        <div class="flex space-x-4">
                            <select id="revenue-period" class="rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                                <option value="7">Last 7 Days</option>
                                <option value="30">Last 30 Days</option>
                                <option value="90">Last 90 Days</option>
                                <option value="365">Last Year</option>
                            </select>
                            <button onclick="exportRevenueReport()" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2">
                                Export Report
                            </button>
                        </div>
                    </div>

                    <!-- Revenue Metrics -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h3 class="text-sm font-medium text-gray-500">Total Revenue</h3>
                            <p class="text-2xl font-bold text-gray-900 mt-2" id="total-revenue">GH₵ 0.00</p>
                            <p class="text-sm text-gray-500 mt-1" id="revenue-change">+0% from previous period</p>
                        </div>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h3 class="text-sm font-medium text-gray-500">Average Daily Revenue</h3>
                            <p class="text-2xl font-bold text-gray-900 mt-2" id="avg-daily-revenue">GH₵ 0.00</p>
                            <p class="text-sm text-gray-500 mt-1" id="avg-revenue-change">+0% from previous period</p>
                        </div>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h3 class="text-sm font-medium text-gray-500">Highest Revenue Day</h3>
                            <p class="text-2xl font-bold text-gray-900 mt-2" id="highest-revenue">GH₵ 0.00</p>
                            <p class="text-sm text-gray-500 mt-1" id="highest-revenue-date">Date: -</p>
                        </div>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h3 class="text-sm font-medium text-gray-500">Revenue per Booking</h3>
                            <p class="text-2xl font-bold text-gray-900 mt-2" id="revenue-per-booking">GH₵ 0.00</p>
                            <p class="text-sm text-gray-500 mt-1" id="revenue-per-booking-change">+0% from previous period</p>
                        </div>
                    </div>

                    <!-- Revenue by Route -->
                    <div class="mb-6">
                        <h3 class="text-md font-medium text-gray-900 mb-4">Revenue by Route</h3>
                        <div class="overflow-x-auto">
                            <table class="min-w-full">
                                <thead>
                                    <tr class="border-b">
                                        <th class="text-left py-2">Route</th>
                                        <th class="text-right py-2">Total Revenue</th>
                                        <th class="text-right py-2">Bookings</th>
                                        <th class="text-right py-2">Avg. Revenue/Booking</th>
                                        <th class="text-right py-2">% of Total</th>
                                    </tr>
                                </thead>
                                <tbody id="revenue-by-route">
                                    <!-- Will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Monthly Comparison -->
                    <div>
                        <h3 class="text-md font-medium text-gray-900 mb-4">Monthly Revenue Comparison</h3>
                        <div class="chart-container">
                            <canvas id="monthly-revenue-chart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Recent Bookings -->
                    <div class="bg-white rounded-lg shadow">
                        <div class="p-6">
                            <h2 class="text-lg font-medium text-gray-900 mb-4">Recent Bookings</h2>
                            <div class="overflow-x-auto">
                                <table class="min-w-full">
                                    <thead>
                                        <tr class="border-b">
                                            <th class="text-left py-2">Booking ID</th>
                                            <th class="text-left py-2">Customer</th>
                                            <th class="text-left py-2">Route</th>
                                            <th class="text-right py-2">Amount</th>
                                        </tr>
                                    </thead>
                                    <tbody id="recent-bookings">
                                        <!-- Recent bookings will be populated here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Popular Routes -->
                    <div class="bg-white rounded-lg shadow">
                        <div class="p-6">
                            <h2 class="text-lg font-medium text-gray-900 mb-4">Popular Routes</h2>
                            <div class="overflow-x-auto">
                                <table class="min-w-full">
                                    <thead>
                                        <tr class="border-b">
                                            <th class="text-left py-2">Route</th>
                                            <th class="text-center py-2">Bookings</th>
                                            <th class="text-right py-2">Revenue</th>
                                        </tr>
                                    </thead>
                                    <tbody id="popular-routes">
                                        <!-- Popular routes will be populated here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        let ws;
        let revenueChart;
        let bookingChart;
        let monthlyRevenueChart;
        const adminUsername = localStorage.getItem('adminUser');
        const adminUsernameElement = document.getElementById('admin-username');
        if (adminUsernameElement) {
            adminUsernameElement.textContent = adminUsername;
        }

        function connectWebSocket() {
            ws = new WebSocket(currentConfig.wsUrl);
            
            ws.onopen = () => {
                console.log('Connected to WebSocket server');
                requestDashboardUpdate();
            };
            
            ws.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    console.log('Received WebSocket message:', data);

                    if (data.type === 'DASHBOARD_UPDATE') {
                        updateDashboard(data.data);
                    } else if (data.type === 'REVENUE_REPORT') {
                        handleRevenueReport(data.data);
                    } else if (data.type === 'ERROR') {
                        showError(data.error);
                    }
                } catch (error) {
                    console.error('Error handling WebSocket message:', error);
                }
            };

            ws.onclose = () => {
                console.log('Disconnected from WebSocket server. Reconnecting...');
                setTimeout(connectWebSocket, 5000);
            };
        }

        function updateDashboard(data) {
            try {
                // Update stats
                if (data.stats) {
                    document.getElementById('total-bookings').textContent = data.stats.totalBookings.toLocaleString();
                    document.getElementById('total-routes').textContent = data.stats.totalRoutes.toLocaleString();
                    document.getElementById('total-scans').textContent = data.stats.totalScans.toLocaleString();
                    document.getElementById('today-revenue').textContent = 
                        `GH₵ ${data.stats.todayRevenue.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}`;
                }

                // Update recent bookings
                if (data.recentBookings) {
                    const bookingsTable = document.getElementById('recent-bookings');
                    bookingsTable.innerHTML = data.recentBookings.map(booking => `
                        <tr class="border-b hover:bg-gray-50">
                            <td class="py-2">${booking.id.slice(0, 8)}...</td>
                            <td class="py-2">${booking.customer_name}</td>
                            <td class="py-2">${booking.routes.from_location} → ${booking.routes.to_location}</td>
                            <td class="py-2 text-right">GH₵ ${booking.total_amount.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>
                        </tr>
                    `).join('');
                }

                // Update popular routes
                if (data.popularRoutes) {
                    const routesTable = document.getElementById('popular-routes');
                    routesTable.innerHTML = data.popularRoutes.map(route => `
                        <tr class="border-b hover:bg-gray-50">
                            <td class="py-2">${route.from_location} → ${route.to_location}</td>
                            <td class="py-2 text-center">${route.total_bookings.toLocaleString()}</td>
                            <td class="py-2 text-right">GH₵ ${route.total_revenue.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>
                        </tr>
                    `).join('');
                }

                // Update revenue chart
                if (data.revenueData) {
                    updateRevenueChart(data.revenueData);
                }

                // Update booking trends chart
                if (data.bookingTrends) {
                    updateBookingChart(data.bookingTrends);
                }

                // Update revenue analytics
                if (data.revenueAnalytics) {
                    updateRevenueAnalytics(data.revenueAnalytics);
                }
            } catch (error) {
                console.error('Error updating dashboard:', error);
                showError('Failed to update dashboard data');
            }
        }

        function updateRevenueChart(data) {
            const ctx = document.getElementById('revenue-chart').getContext('2d');
            
            if (revenueChart) {
                revenueChart.destroy();
            }

            revenueChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.map(day => new Date(day.date).toLocaleDateString()),
                    datasets: [{
                        label: 'Daily Revenue (GH₵)',
                        data: data.map(day => day.revenue),
                        borderColor: '#1a56db',
                        backgroundColor: 'rgba(26, 86, 219, 0.1)',
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `GH₵ ${context.raw.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}`;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return 'GH₵ ' + value.toLocaleString();
                                }
                            }
                        }
                    }
                }
            });
        }

        function updateBookingChart(data) {
            const ctx = document.getElementById('booking-chart').getContext('2d');
            
            if (bookingChart) {
                bookingChart.destroy();
            }

            bookingChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: data.map(day => new Date(day.date).toLocaleDateString()),
                    datasets: [{
                        label: 'Daily Bookings',
                        data: data.map(day => day.bookings),
                        backgroundColor: '#1a56db',
                        borderRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    }
                }
            });
        }

        function updateRevenueAnalytics(data) {
            // Update revenue metrics
            document.getElementById('total-revenue').textContent = 
                `GH₵ ${data.totalRevenue.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}`;
            document.getElementById('revenue-change').textContent = 
                `${data.revenueChange >= 0 ? '+' : ''}${data.revenueChange}% from previous period`;
            document.getElementById('revenue-change').className = 
                `text-sm ${data.revenueChange >= 0 ? 'text-green-500' : 'text-red-500'} mt-1`;

            document.getElementById('avg-daily-revenue').textContent = 
                `GH₵ ${data.avgDailyRevenue.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}`;
            document.getElementById('avg-revenue-change').textContent = 
                `${data.avgRevenueChange >= 0 ? '+' : ''}${data.avgRevenueChange}% from previous period`;
            document.getElementById('avg-revenue-change').className = 
                `text-sm ${data.avgRevenueChange >= 0 ? 'text-green-500' : 'text-red-500'} mt-1`;

            document.getElementById('highest-revenue').textContent = 
                `GH₵ ${data.highestRevenue.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}`;
            document.getElementById('highest-revenue-date').textContent = 
                `Date: ${new Date(data.highestRevenueDate).toLocaleDateString()}`;

            document.getElementById('revenue-per-booking').textContent = 
                `GH₵ ${data.revenuePerBooking.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}`;
            document.getElementById('revenue-per-booking-change').textContent = 
                `${data.revenuePerBookingChange >= 0 ? '+' : ''}${data.revenuePerBookingChange}% from previous period`;
            document.getElementById('revenue-per-booking-change').className = 
                `text-sm ${data.revenuePerBookingChange >= 0 ? 'text-green-500' : 'text-red-500'} mt-1`;

            // Update revenue by route table
            const revenueByRouteTable = document.getElementById('revenue-by-route');
            revenueByRouteTable.innerHTML = data.revenueByRoute.map(route => `
                <tr class="border-b hover:bg-gray-50">
                    <td class="py-2">${route.from_location} → ${route.to_location}</td>
                    <td class="py-2 text-right">GH₵ ${route.totalRevenue.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>
                    <td class="py-2 text-right">${route.bookings.toLocaleString()}</td>
                    <td class="py-2 text-right">GH₵ ${route.avgRevenuePerBooking.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>
                    <td class="py-2 text-right">${route.percentageOfTotal.toFixed(1)}%</td>
                </tr>
            `).join('');

            // Update monthly comparison chart
            updateMonthlyRevenueChart(data.monthlyComparison);
        }

        function updateMonthlyRevenueChart(data) {
            const ctx = document.getElementById('monthly-revenue-chart').getContext('2d');
            
            if (monthlyRevenueChart) {
                monthlyRevenueChart.destroy();
            }

            monthlyRevenueChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: data.map(month => month.month),
                    datasets: [{
                        label: 'This Year',
                        data: data.map(month => month.thisYear),
                        backgroundColor: '#1a56db',
                        borderRadius: 4
                    }, {
                        label: 'Last Year',
                        data: data.map(month => month.lastYear),
                        backgroundColor: '#e5e7eb',
                        borderRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `GH₵ ${context.raw.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}`;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return 'GH₵ ' + value.toLocaleString();
                                }
                            }
                        }
                    }
                }
            });
        }

        function exportRevenueReport() {
            const period = document.getElementById('revenue-period').value;
            
            if (ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({ 
                    type: 'EXPORT_REVENUE_REPORT',
                    data: { period }
                }));
                
                Swal.fire({
                    title: 'Generating Report',
                    text: 'Please wait while we generate your report...',
                    allowOutsideClick: false,
                    showConfirmButton: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });
            }
        }

        function handleRevenueReport(reportData) {
            // Create CSV content
            let csvContent = 'ABC Travels Revenue Report\n';
            csvContent += `Generated at: ${new Date(reportData.generatedAt).toLocaleString()}\n`;
            csvContent += `Period: ${reportData.period}\n\n`;
            
            // Add metrics
            csvContent += 'Revenue Metrics\n';
            csvContent += `Total Revenue,GH₵ ${reportData.metrics.totalRevenue.toFixed(2)}\n`;
            csvContent += `Average Daily Revenue,GH₵ ${reportData.metrics.avgDailyRevenue.toFixed(2)}\n`;
            csvContent += `Revenue Change,${reportData.metrics.revenueChange.toFixed(2)}%\n`;
            csvContent += `Highest Revenue,GH₵ ${reportData.metrics.highestRevenue.toFixed(2)}\n`;
            csvContent += `Highest Revenue Date,${new Date(reportData.metrics.highestRevenueDate).toLocaleDateString()}\n`;
            csvContent += `Revenue per Booking,GH₵ ${reportData.metrics.revenuePerBooking.toFixed(2)}\n\n`;
            
            // Add revenue by route
            csvContent += 'Revenue by Route\n';
            csvContent += 'Route,Total Revenue,Bookings,Average Revenue/Booking,% of Total\n';
            reportData.revenueByRoute.forEach(route => {
                csvContent += `${route.from_location} → ${route.to_location},`;
                csvContent += `GH₵ ${route.totalRevenue.toFixed(2)},`;
                csvContent += `${route.bookings},`;
                csvContent += `GH₵ ${route.avgRevenuePerBooking.toFixed(2)},`;
                csvContent += `${route.percentageOfTotal.toFixed(1)}%\n`;
            });
            
            // Add monthly comparison
            csvContent += '\nMonthly Comparison\n';
            csvContent += 'Month,This Year,Last Year\n';
            reportData.monthlyComparison.forEach(month => {
                csvContent += `${month.month},`;
                csvContent += `GH₵ ${month.thisYear.toFixed(2)},`;
                csvContent += `GH₵ ${month.lastYear.toFixed(2)}\n`;
            });
            
            // Create and download the CSV file
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `ABC_Travels_Revenue_Report_${new Date().toISOString().split('T')[0]}.csv`);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            Swal.fire({
                icon: 'success',
                title: 'Report Generated',
                text: 'Your revenue report has been downloaded.',
                confirmButtonColor: '#1a56db'
            });
        }

        // Add event listener for period change
        document.getElementById('revenue-period').addEventListener('change', function() {
            const period = this.value;
            if (ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({ 
                    type: 'REQUEST_UPDATE',
                    data: { period }
                }));
            }
        });

        function requestDashboardUpdate() {
            if (ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({ type: 'REQUEST_UPDATE' }));
            }
        }

        function showError(message) {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: message,
                confirmButtonColor: '#1a56db'
            });
        }

        function logout() {
            localStorage.removeItem('adminAuthenticated');
            localStorage.removeItem('adminUser');
            localStorage.removeItem('adminToken');
            window.location.href = 'login.html';
        }

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', () => {
            connectWebSocket();
            
            // Set up periodic updates
            setInterval(requestDashboardUpdate, 30000); // Update every 30 seconds
        });

        // Mobile Menu
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('overlay');
        const menuIcon = mobileMenuButton.querySelector('.menu-icon');

        mobileMenuButton.addEventListener('click', () => {
            sidebar.classList.toggle('-translate-x-full');
            overlay.classList.toggle('hidden');
            menuIcon.classList.toggle('active');
        });

        overlay.addEventListener('click', () => {
            sidebar.classList.add('-translate-x-full');
            overlay.classList.add('hidden');
            menuIcon.classList.remove('active');
        });
    </script>
</body>
</html> 