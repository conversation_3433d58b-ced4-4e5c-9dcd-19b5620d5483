Product Requirements Document (PRD) for ABC Travels Bus Ticketing Website

1. Overview
ABC Travels is a bus ticketing website developed by Procurement and Supply Group 4. The platform enables users to book bus tickets online seamlessly, ensuring a user-friendly, responsive, and cross-browser-compatible experience. The website will be hosted on Render (backend), Netlify (frontend), and MongoDB Atlas (database).

2. User Side

2.1 Pages

Home

Routes

About

FAQ

Privacy Policy

Contact

2.2 Home Page Structure (7 Sections - Industry Standard)

Hero Section: Prominent section providing a clear, direct introduction.

How It Works: Simple steps outlining the ticket booking process.

Popular Routes: Displays popular bus routes dynamically.

Why Choose Us: Highlights key benefits and features.

Customer Testimonials: Real reviews from past travelers.

Partner Companies: Logos and names of bus operators partnered with ABC Travels.

Call-to-Action: Encourages users to book tickets immediately.

2.3 About Page

Describes Procurement and Supply Group 4 as the team behind the platform.

Outlines their mission to create a reliable bus ticketing system for Ghana.

Emphasizes their dedication to improving transportation convenience.

2.4 Booking Process

User selects a route, triggering a booking form.

User fills in details and proceeds to Paystack payment gateway.

Upon payment confirmation:

A QR code is generated and downloaded.

A copy of the QR code is sent to the user's email.

2.5 Functionality and Features

No user accounts required.

Completely responsive and cross-browser compatible.

Available routes dynamically fetched from the database.

Secure and seamless Paystack integration for payments.

QR codes can only be scanned once by admin.

3. Admin Side

3.1 Pages (Navlinks not included in User Side Navbar)

Dashboard: Displays real-time statistics and user bookings.

Routes: Admin can add and manage bus routes.

Bookings: Shows all ticket purchases and details.

QR Scanner: Verifies ticket authenticity and marks them as used.

Logout: Ends admin session.

3.2 Admin Authentication

Mandatory login before accessing any admin functionalities.

Secure authentication to prevent unauthorized access.

3.3 Route Management

Admin adds routes that automatically appear on both the Admin Panel and User Side.

Can select a range of available bus times and days to avoid redundant entries.

Routes include departure location, destination, time slots, price, and availability.

3.4 QR Code System

Users receive a scannable QR code after successful payment.

Admin scans the QR code using the QR Scanner Page.

Once scanned, it updates the admin panel, marking the ticket as used.

QR codes can only be scanned once.

3.5 Dashboard Overview

Displays:

Total bookings

Available routes

Revenue statistics

Scanned and unscanned QR codes

Pending ticket verifications

4. Technology Stack

Frontend: HTML, CSS, JavaScript

Backend Hosting: Render

Frontend Hosting: Netlify

Database: MongoDB Atlas

Payment Gateway: Paystack

QR Code Generation: JavaScript QR code libraries

5. Key Requirements

Comprehensive responsiveness across all devices.

Seamless UI/UX following modern industry standards.

Cross-browser compatibility ensuring a uniform experience.

Robust admin controls for secure and efficient management.

Secure payment processing with Paystack integration.

Real-time updates on bookings and route availability.

Efficient QR scanning system to validate tickets accurately.

6. Deployment Strategy

Frontend will be deployed on Netlify.

Backend services will run on Render.

Database will be managed via MongoDB Atlas.

Regular testing for functionality, security, and performance.